{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\Contact.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport axios from 'axios';\nimport { FaEnvelope, FaPhone, FaMapMarkerAlt, FaGithub, FaLinkedin, FaTwitter, FaPaperPlane, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Contact = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: '',\n    budget: '',\n    timeline: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState(null);\n  const [focusedField, setFocusedField] = useState(null);\n  const handleChange = e => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    try {\n      await axios.post('http://localhost:5001/api/contact', formData);\n      setSubmitStatus('success');\n      setFormData({\n        name: '',\n        email: '',\n        subject: '',\n        message: ''\n      });\n    } catch (error) {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n      setTimeout(() => setSubmitStatus(null), 5000);\n    }\n  };\n  const contactInfo = [{\n    icon: '📧',\n    title: 'Email',\n    value: '<EMAIL>',\n    description: 'Send me an email anytime'\n  }, {\n    icon: '📱',\n    title: 'Phone',\n    value: '+****************',\n    description: 'Call me for urgent matters'\n  }, {\n    icon: '📍',\n    title: 'Location',\n    value: 'San Francisco, CA',\n    description: 'Available for remote work'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"contact\",\n    className: \"py-20 bg-gradient-to-br from-gray-50 to-blue-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: \"Get In Touch\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Ready to start your next project? Let's work together to create something amazing.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -50\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-900 mb-8\",\n            children: \"Let's Connect\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-8 leading-relaxed\",\n            children: \"I'm always interested in new opportunities and exciting projects. Whether you have a question, want to collaborate, or just want to say hi, I'd love to hear from you!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: contactInfo.map((info, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              whileInView: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                duration: 0.5,\n                delay: index * 0.1\n              },\n              className: \"flex items-start space-x-4 p-4 bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl\",\n                children: info.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-gray-900 mb-1\",\n                  children: info.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-blue-600 font-medium mb-1\",\n                  children: info.value\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 106,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-500 text-sm\",\n                  children: info.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 107,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this)]\n            }, info.title, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.4\n            },\n            className: \"mt-8\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-semibold text-gray-900 mb-4\",\n              children: \"Follow Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-4\",\n              children: [{\n                name: 'GitHub',\n                icon: 'M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z'\n              }, {\n                name: 'LinkedIn',\n                icon: 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'\n              }, {\n                name: 'Twitter',\n                icon: 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z'\n              }].map((social, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n                href: \"#\",\n                className: \"w-12 h-12 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-blue-600 hover:shadow-lg transition-all duration-300\",\n                whileHover: {\n                  scale: 1.1,\n                  y: -2\n                },\n                whileTap: {\n                  scale: 0.9\n                },\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: social.icon\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 21\n                }, this)\n              }, social.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 127,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 50\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"bg-white rounded-2xl shadow-xl p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-gray-900 mb-6\",\n            children: \"Send Message\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                  children: \"Name *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 155,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"text\",\n                  name: \"name\",\n                  value: formData.name,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n                  placeholder: \"Your name\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                  className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                  children: \"Email *\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"email\",\n                  name: \"email\",\n                  value: formData.email,\n                  onChange: handleChange,\n                  required: true,\n                  className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n                  placeholder: \"<EMAIL>\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                children: \"Subject *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n                type: \"text\",\n                name: \"subject\",\n                value: formData.subject,\n                onChange: handleChange,\n                required: true,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\",\n                placeholder: \"What's this about?\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"block text-sm font-semibold text-gray-700 mb-2\",\n                children: \"Message *\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"textarea\", {\n                name: \"message\",\n                value: formData.message,\n                onChange: handleChange,\n                required: true,\n                rows: 6,\n                className: \"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none\",\n                placeholder: \"Tell me about your project...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              type: \"submit\",\n              disabled: isSubmitting,\n              className: \"w-full py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-semibold hover:shadow-lg transition-all duration-300 disabled:opacity-50\",\n              whileHover: {\n                scale: 1.02\n              },\n              whileTap: {\n                scale: 0.98\n              },\n              children: isSubmitting ? 'Sending...' : 'Send Message'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 214,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), submitStatus === 'success' && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 10\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mt-4 p-4 bg-green-50 border border-green-200 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-green-800\",\n              children: \"\\u2705 Message sent successfully! I'll get back to you soon.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 15\n          }, this), submitStatus === 'error' && /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 10\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            className: \"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-800\",\n              children: \"\\u274C Something went wrong. Please try again.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 80,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 65,\n    columnNumber: 5\n  }, this);\n};\n_s(Contact, \"VVVxW7YtLzH2CzL0KYR8mwlY9Ow=\");\n_c = Contact;\nexport default Contact;\nvar _c;\n$RefreshReg$(_c, \"Contact\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "axios", "FaEnvelope", "FaPhone", "FaMapMarkerAlt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaLinkedin", "FaTwitter", "FaPaperPlane", "FaCheckCircle", "FaExclamationTriangle", "jsxDEV", "_jsxDEV", "Contact", "_s", "formData", "setFormData", "name", "email", "subject", "message", "budget", "timeline", "errors", "setErrors", "isSubmitting", "setIsSubmitting", "submitStatus", "setSubmitStatus", "focusedField", "setFocusedField", "handleChange", "e", "target", "value", "handleSubmit", "preventDefault", "post", "error", "setTimeout", "contactInfo", "icon", "title", "description", "id", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "map", "info", "index", "delay", "social", "a", "href", "whileHover", "scale", "whileTap", "fill", "viewBox", "d", "onSubmit", "type", "onChange", "required", "placeholder", "rows", "button", "disabled", "animate", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/Contact.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport axios from 'axios';\nimport { FaEnvelope, FaPhone, FaMapMarkerAlt, FaGithub, FaLinkedin, FaTwitter, FaPaperPlane, FaCheckCircle, FaExclamationTriangle } from 'react-icons/fa';\n\nconst Contact = () => {\n  const [formData, setFormData] = useState({\n    name: '',\n    email: '',\n    subject: '',\n    message: '',\n    budget: '',\n    timeline: ''\n  });\n  const [errors, setErrors] = useState({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState(null);\n  const [focusedField, setFocusedField] = useState(null);\n\n  const handleChange = (e) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value\n    });\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    try {\n      await axios.post('http://localhost:5001/api/contact', formData);\n      setSubmitStatus('success');\n      setFormData({ name: '', email: '', subject: '', message: '' });\n    } catch (error) {\n      setSubmitStatus('error');\n    } finally {\n      setIsSubmitting(false);\n      setTimeout(() => setSubmitStatus(null), 5000);\n    }\n  };\n\n  const contactInfo = [\n    {\n      icon: '📧',\n      title: 'Email',\n      value: '<EMAIL>',\n      description: 'Send me an email anytime'\n    },\n    {\n      icon: '📱',\n      title: 'Phone',\n      value: '+****************',\n      description: 'Call me for urgent matters'\n    },\n    {\n      icon: '📍',\n      title: 'Location',\n      value: 'San Francisco, CA',\n      description: 'Available for remote work'\n    }\n  ];\n\n  return (\n    <section id=\"contact\" className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">Get In Touch</h2>\n          <div className=\"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"></div>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Ready to start your next project? Let's work together to create something amazing.\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16\">\n          {/* Contact Info */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">Let's Connect</h3>\n            <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n              I'm always interested in new opportunities and exciting projects. \n              Whether you have a question, want to collaborate, or just want to say hi, \n              I'd love to hear from you!\n            </p>\n\n            <div className=\"space-y-6\">\n              {contactInfo.map((info, index) => (\n                <motion.div\n                  key={info.title}\n                  initial={{ opacity: 0, y: 20 }}\n                  whileInView={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  className=\"flex items-start space-x-4 p-4 bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300\"\n                >\n                  <div className=\"text-3xl\">{info.icon}</div>\n                  <div>\n                    <h4 className=\"font-semibold text-gray-900 mb-1\">{info.title}</h4>\n                    <p className=\"text-blue-600 font-medium mb-1\">{info.value}</p>\n                    <p className=\"text-gray-500 text-sm\">{info.description}</p>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Social Links */}\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              className=\"mt-8\"\n            >\n              <h4 className=\"font-semibold text-gray-900 mb-4\">Follow Me</h4>\n              <div className=\"flex space-x-4\">\n                {[\n                  { name: 'GitHub', icon: 'M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z' },\n                  { name: 'LinkedIn', icon: 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z' },\n                  { name: 'Twitter', icon: 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z' }\n                ].map((social, index) => (\n                  <motion.a\n                    key={social.name}\n                    href=\"#\"\n                    className=\"w-12 h-12 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-blue-600 hover:shadow-lg transition-all duration-300\"\n                    whileHover={{ scale: 1.1, y: -2 }}\n                    whileTap={{ scale: 0.9 }}\n                  >\n                    <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path d={social.icon}/>\n                    </svg>\n                  </motion.a>\n                ))}\n              </div>\n            </motion.div>\n          </motion.div>\n\n          {/* Contact Form */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"bg-white rounded-2xl shadow-xl p-8\"\n          >\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-6\">Send Message</h3>\n            \n            <form onSubmit={handleSubmit} className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    name=\"name\"\n                    value={formData.name}\n                    onChange={handleChange}\n                    required\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    placeholder=\"Your name\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                    Email *\n                  </label>\n                  <input\n                    type=\"email\"\n                    name=\"email\"\n                    value={formData.email}\n                    onChange={handleChange}\n                    required\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                    placeholder=\"<EMAIL>\"\n                  />\n                </div>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Subject *\n                </label>\n                <input\n                  type=\"text\"\n                  name=\"subject\"\n                  value={formData.subject}\n                  onChange={handleChange}\n                  required\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200\"\n                  placeholder=\"What's this about?\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-semibold text-gray-700 mb-2\">\n                  Message *\n                </label>\n                <textarea\n                  name=\"message\"\n                  value={formData.message}\n                  onChange={handleChange}\n                  required\n                  rows={6}\n                  className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 resize-none\"\n                  placeholder=\"Tell me about your project...\"\n                ></textarea>\n              </div>\n\n              <motion.button\n                type=\"submit\"\n                disabled={isSubmitting}\n                className=\"w-full py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-semibold hover:shadow-lg transition-all duration-300 disabled:opacity-50\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                {isSubmitting ? 'Sending...' : 'Send Message'}\n              </motion.button>\n            </form>\n\n            {/* Status Messages */}\n            {submitStatus === 'success' && (\n              <motion.div\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mt-4 p-4 bg-green-50 border border-green-200 rounded-lg\"\n              >\n                <p className=\"text-green-800\">✅ Message sent successfully! I'll get back to you soon.</p>\n              </motion.div>\n            )}\n\n            {submitStatus === 'error' && (\n              <motion.div\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg\"\n              >\n                <p className=\"text-red-800\">❌ Something went wrong. Please try again.</p>\n              </motion.div>\n            )}\n          </motion.div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default Contact;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,EAAEC,OAAO,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,YAAY,EAAEC,aAAa,EAAEC,qBAAqB,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1J,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACpB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlB,QAAQ,CAAC;IACvCmB,IAAI,EAAE,EAAE;IACRC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,OAAO,EAAE,EAAE;IACXC,MAAM,EAAE,EAAE;IACVC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAAC2B,YAAY,EAAEC,eAAe,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM,CAAC+B,YAAY,EAAEC,eAAe,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EAEtD,MAAMiC,YAAY,GAAIC,CAAC,IAAK;IAC1BhB,WAAW,CAAC;MACV,GAAGD,QAAQ;MACX,CAACiB,CAAC,CAACC,MAAM,CAAChB,IAAI,GAAGe,CAAC,CAACC,MAAM,CAACC;IAC5B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,YAAY,GAAG,MAAOH,CAAC,IAAK;IAChCA,CAAC,CAACI,cAAc,CAAC,CAAC;IAClBV,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACF,MAAMzB,KAAK,CAACoC,IAAI,CAAC,mCAAmC,EAAEtB,QAAQ,CAAC;MAC/Da,eAAe,CAAC,SAAS,CAAC;MAC1BZ,WAAW,CAAC;QAAEC,IAAI,EAAE,EAAE;QAAEC,KAAK,EAAE,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,OAAO,EAAE;MAAG,CAAC,CAAC;IAChE,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdV,eAAe,CAAC,OAAO,CAAC;IAC1B,CAAC,SAAS;MACRF,eAAe,CAAC,KAAK,CAAC;MACtBa,UAAU,CAAC,MAAMX,eAAe,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC;IAC/C;EACF,CAAC;EAED,MAAMY,WAAW,GAAG,CAClB;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,OAAO;IACdR,KAAK,EAAE,kBAAkB;IACzBS,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,OAAO;IACdR,KAAK,EAAE,mBAAmB;IAC1BS,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,UAAU;IACjBR,KAAK,EAAE,mBAAmB;IAC1BS,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACE/B,OAAA;IAASgC,EAAE,EAAC,SAAS;IAACC,SAAS,EAAC,iDAAiD;IAAAC,QAAA,eAC/ElC,OAAA;MAAKiC,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDlC,OAAA,CAACb,MAAM,CAACgD,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BlC,OAAA;UAAIiC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvE7C,OAAA;UAAKiC,SAAS,EAAC;QAAoE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1F7C,OAAA;UAAGiC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEb7C,OAAA;QAAKiC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBAErDlC,OAAA,CAACb,MAAM,CAACgD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCP,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,gBAE9BlC,OAAA;YAAIiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAa;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxE7C,OAAA;YAAGiC,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAI1D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJ7C,OAAA;YAAKiC,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBN,WAAW,CAACmB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC3BjD,OAAA,CAACb,MAAM,CAACgD,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/BC,WAAW,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAClCE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAES,KAAK,EAAED,KAAK,GAAG;cAAI,CAAE;cAClDhB,SAAS,EAAC,6GAA6G;cAAAC,QAAA,gBAEvHlC,OAAA;gBAAKiC,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAEc,IAAI,CAACnB;cAAI;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3C7C,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAIiC,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAEc,IAAI,CAAClB;gBAAK;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClE7C,OAAA;kBAAGiC,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,EAAEc,IAAI,CAAC1B;gBAAK;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9D7C,OAAA;kBAAGiC,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAEc,IAAI,CAACjB;gBAAW;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxD,CAAC;YAAA,GAXDG,IAAI,CAAClB,KAAK;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYL,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN7C,OAAA,CAACb,MAAM,CAACgD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAES,KAAK,EAAE;YAAI,CAAE;YAC1CjB,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAEhBlC,OAAA;cAAIiC,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAS;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC/D7C,OAAA;cAAKiC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5B,CACC;gBAAE7B,IAAI,EAAE,QAAQ;gBAAEwB,IAAI,EAAE;cAA4sB,CAAC,EACruB;gBAAExB,IAAI,EAAE,UAAU;gBAAEwB,IAAI,EAAE;cAAqf,CAAC,EAChhB;gBAAExB,IAAI,EAAE,SAAS;gBAAEwB,IAAI,EAAE;cAA8e,CAAC,CACzgB,CAACkB,GAAG,CAAC,CAACI,MAAM,EAAEF,KAAK,kBAClBjD,OAAA,CAACb,MAAM,CAACiE,CAAC;gBAEPC,IAAI,EAAC,GAAG;gBACRpB,SAAS,EAAC,gJAAgJ;gBAC1JqB,UAAU,EAAE;kBAAEC,KAAK,EAAE,GAAG;kBAAEjB,CAAC,EAAE,CAAC;gBAAE,CAAE;gBAClCkB,QAAQ,EAAE;kBAAED,KAAK,EAAE;gBAAI,CAAE;gBAAArB,QAAA,eAEzBlC,OAAA;kBAAKiC,SAAS,EAAC,SAAS;kBAACwB,IAAI,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAxB,QAAA,eAC9DlC,OAAA;oBAAM2D,CAAC,EAAER,MAAM,CAACtB;kBAAK;oBAAAa,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC,GARDM,MAAM,CAAC9C,IAAI;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OASR,CACX;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGb7C,OAAA,CAACb,MAAM,CAACgD,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BP,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BR,SAAS,EAAC,oCAAoC;UAAAC,QAAA,gBAE9ClC,OAAA;YAAIiC,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEvE7C,OAAA;YAAM4D,QAAQ,EAAErC,YAAa;YAACU,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACjDlC,OAAA;cAAKiC,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlC,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAElE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBACE6D,IAAI,EAAC,MAAM;kBACXxD,IAAI,EAAC,MAAM;kBACXiB,KAAK,EAAEnB,QAAQ,CAACE,IAAK;kBACrByD,QAAQ,EAAE3C,YAAa;kBACvB4C,QAAQ;kBACR9B,SAAS,EAAC,0IAA0I;kBACpJ+B,WAAW,EAAC;gBAAW;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eACN7C,OAAA;gBAAAkC,QAAA,gBACElC,OAAA;kBAAOiC,SAAS,EAAC,gDAAgD;kBAAAC,QAAA,EAAC;gBAElE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACR7C,OAAA;kBACE6D,IAAI,EAAC,OAAO;kBACZxD,IAAI,EAAC,OAAO;kBACZiB,KAAK,EAAEnB,QAAQ,CAACG,KAAM;kBACtBwD,QAAQ,EAAE3C,YAAa;kBACvB4C,QAAQ;kBACR9B,SAAS,EAAC,0IAA0I;kBACpJ+B,WAAW,EAAC;gBAAgB;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7B,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEN7C,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAOiC,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAElE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACE6D,IAAI,EAAC,MAAM;gBACXxD,IAAI,EAAC,SAAS;gBACdiB,KAAK,EAAEnB,QAAQ,CAACI,OAAQ;gBACxBuD,QAAQ,EAAE3C,YAAa;gBACvB4C,QAAQ;gBACR9B,SAAS,EAAC,0IAA0I;gBACpJ+B,WAAW,EAAC;cAAoB;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAEN7C,OAAA;cAAAkC,QAAA,gBACElC,OAAA;gBAAOiC,SAAS,EAAC,gDAAgD;gBAAAC,QAAA,EAAC;cAElE;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,eACR7C,OAAA;gBACEK,IAAI,EAAC,SAAS;gBACdiB,KAAK,EAAEnB,QAAQ,CAACK,OAAQ;gBACxBsD,QAAQ,EAAE3C,YAAa;gBACvB4C,QAAQ;gBACRE,IAAI,EAAE,CAAE;gBACRhC,SAAS,EAAC,sJAAsJ;gBAChK+B,WAAW,EAAC;cAA+B;gBAAAtB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEN7C,OAAA,CAACb,MAAM,CAAC+E,MAAM;cACZL,IAAI,EAAC,QAAQ;cACbM,QAAQ,EAAEtD,YAAa;cACvBoB,SAAS,EAAC,8JAA8J;cACxKqB,UAAU,EAAE;gBAAEC,KAAK,EAAE;cAAK,CAAE;cAC5BC,QAAQ,EAAE;gBAAED,KAAK,EAAE;cAAK,CAAE;cAAArB,QAAA,EAEzBrB,YAAY,GAAG,YAAY,GAAG;YAAc;cAAA6B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,EAGN9B,YAAY,KAAK,SAAS,iBACzBf,OAAA,CAACb,MAAM,CAACgD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/B8B,OAAO,EAAE;cAAE/B,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BL,SAAS,EAAC,yDAAyD;YAAAC,QAAA,eAEnElC,OAAA;cAAGiC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAuD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/E,CACb,EAEA9B,YAAY,KAAK,OAAO,iBACvBf,OAAA,CAACb,MAAM,CAACgD,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/B8B,OAAO,EAAE;cAAE/B,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAC9BL,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAE/DlC,OAAA;cAAGiC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAyC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CACb;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC3C,EAAA,CApPID,OAAO;AAAAoE,EAAA,GAAPpE,OAAO;AAsPb,eAAeA,OAAO;AAAC,IAAAoE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}