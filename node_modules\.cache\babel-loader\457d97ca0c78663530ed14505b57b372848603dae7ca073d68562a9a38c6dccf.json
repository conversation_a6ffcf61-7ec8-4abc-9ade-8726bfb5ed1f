{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\About.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGraduationCap, FaBriefcase, FaAward, FaHeart, FaCode, FaRocket, FaUsers, FaLightbulb } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('story');\n  const stats = [{\n    number: '5+',\n    label: 'Years Experience',\n    icon: FaRocket,\n    color: 'from-blue-500 to-cyan-500'\n  }, {\n    number: '50+',\n    label: 'Projects Completed',\n    icon: FaBriefcase,\n    color: 'from-green-500 to-emerald-500'\n  }, {\n    number: '15+',\n    label: 'Technologies',\n    icon: FaCode,\n    color: 'from-purple-500 to-pink-500'\n  }, {\n    number: '100%',\n    label: 'Client Satisfaction',\n    icon: FaHeart,\n    color: 'from-red-500 to-rose-500'\n  }];\n  const timeline = [{\n    year: '2023',\n    title: 'Senior Full Stack Developer',\n    company: 'Tech Innovations Inc.',\n    description: 'Leading development of enterprise-scale applications, mentoring junior developers, and architecting scalable solutions.',\n    icon: FaBriefcase,\n    type: 'work'\n  }, {\n    year: '2022',\n    title: 'AWS Certified Developer',\n    company: 'Amazon Web Services',\n    description: 'Achieved AWS certification demonstrating expertise in cloud architecture and serverless technologies.',\n    icon: FaAward,\n    type: 'achievement'\n  }, {\n    year: '2021',\n    title: 'Full Stack Developer',\n    company: 'Digital Solutions Ltd.',\n    description: 'Developed and maintained multiple client projects using React, Node.js, and cloud technologies.',\n    icon: FaBriefcase,\n    type: 'work'\n  }, {\n    year: '2020',\n    title: 'Computer Science Degree',\n    company: 'University of Technology',\n    description: 'Graduated with honors, specializing in software engineering and web technologies.',\n    icon: FaGraduationCap,\n    type: 'education'\n  }];\n  const values = [{\n    icon: FaLightbulb,\n    title: 'Innovation',\n    description: 'Always exploring new technologies and creative solutions to complex problems.'\n  }, {\n    icon: FaUsers,\n    title: 'Collaboration',\n    description: 'Believing in the power of teamwork and open communication to achieve great results.'\n  }, {\n    icon: FaRocket,\n    title: 'Excellence',\n    description: 'Committed to delivering high-quality code and exceptional user experiences.'\n  }, {\n    icon: FaHeart,\n    title: 'Passion',\n    description: 'Genuinely passionate about technology and its potential to make a positive impact.'\n  }];\n  const tabs = [{\n    id: 'story',\n    label: 'My Story',\n    icon: FaHeart\n  }, {\n    id: 'journey',\n    label: 'Journey',\n    icon: FaRocket\n  }, {\n    id: 'values',\n    label: 'Values',\n    icon: FaLightbulb\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full blur-3xl\",\n        animate: {\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.span, {\n          className: \"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          whileInView: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            delay: 0.2\n          },\n          children: \"\\uD83D\\uDC4B Get to know me\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n          children: [\"About\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n            children: \" Me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"w-20 h-1 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto mb-6\",\n          initial: {\n            width: 0\n          },\n          whileInView: {\n            width: 80\n          },\n          transition: {\n            delay: 0.4,\n            duration: 0.8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-400 max-w-3xl mx-auto\",\n          children: \"Passionate developer, creative problem solver, and technology enthusiast dedicated to building exceptional digital experiences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"grid grid-cols-2 md:grid-cols-4 gap-6 mb-20\",\n        children: stats.map((stat, index) => {\n          const IconComponent = stat.icon;\n          return /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.5\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            whileHover: {\n              scale: 1.05,\n              y: -5\n            },\n            className: \"bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 text-center hover:border-blue-500/50 transition-all duration-300 group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `w-16 h-16 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`,\n              children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                className: \"w-8 h-8 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 149,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-white mb-2\",\n              children: stat.number\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-gray-400 font-medium\",\n              children: stat.label\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, stat.label, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 131,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"flex justify-center mb-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-full p-2 shadow-xl\",\n          children: tabs.map((tab, index) => {\n            const IconComponent = tab.icon;\n            return /*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: () => setActiveTab(tab.id),\n              className: `relative px-6 py-3 rounded-full font-semibold transition-all duration-300 flex items-center gap-2 ${activeTab === tab.id ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg' : 'text-gray-400 hover:text-white hover:bg-gray-700/50'}`,\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              initial: {\n                opacity: 0,\n                y: 20\n              },\n              animate: {\n                opacity: 1,\n                y: 0\n              },\n              transition: {\n                delay: index * 0.1\n              },\n              children: [/*#__PURE__*/_jsxDEV(IconComponent, {\n                className: \"w-4 h-4\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this), tab.label, activeTab === tab.id && /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full\",\n                layoutId: \"activeAboutTab\",\n                transition: {\n                  type: \"spring\",\n                  bounce: 0.2,\n                  duration: 0.6\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 21\n              }, this)]\n            }, tab.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 17\n            }, this);\n          })\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -20\n          },\n          transition: {\n            duration: 0.5\n          },\n          className: \"bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-3xl p-8 mb-20\",\n          children: [activeTab === 'story' && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-3xl font-bold text-white mb-6\",\n                children: \"Full Stack Developer & Creative Problem Solver\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 212,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg text-gray-300 mb-6 leading-relaxed\",\n                children: \"I'm a passionate full-stack developer with over 5 years of experience creating digital solutions that make a real difference. My journey in tech started with curiosity about how websites work and has evolved into a career dedicated to building applications that are both beautiful and functional.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 216,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg text-gray-300 mb-6 leading-relaxed\",\n                children: \"I specialize in modern web technologies including React, Node.js, and cloud platforms. I believe in writing clean, maintainable code and creating user experiences that delight and inspire. Every project is an opportunity to learn something new and push the boundaries of what's possible.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-lg text-gray-300 leading-relaxed\",\n                children: \"When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or mentoring aspiring developers. I'm always excited to take on new challenges and collaborate with teams that share my passion for innovation.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 224,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"w-80 h-80 mx-auto bg-gradient-to-br from-gray-800 to-gray-900 rounded-full border-4 border-gray-700 overflow-hidden shadow-2xl\",\n                whileHover: {\n                  scale: 1.05\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 flex items-center justify-center\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-8xl text-gray-600\",\n                    children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBB\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 15\n          }, this), activeTab === 'journey' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-3xl font-bold text-white mb-8 text-center\",\n              children: \"My Professional Journey\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 246,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-8\",\n              children: timeline.map((item, index) => {\n                const IconComponent = item.icon;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -50\n                  },\n                  animate: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: index * 0.2\n                  },\n                  className: \"flex items-start gap-6 group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-16 h-16 rounded-full flex items-center justify-center flex-shrink-0 ${item.type === 'work' ? 'bg-blue-500' : item.type === 'education' ? 'bg-green-500' : 'bg-purple-500'} group-hover:scale-110 transition-transform duration-300`,\n                    children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                      className: \"w-8 h-8 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 262,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 258,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center gap-4 mb-2\",\n                      children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm font-bold text-blue-400 bg-blue-500/20 px-3 py-1 rounded-full\",\n                        children: item.year\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 267,\n                        columnNumber: 29\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: `text-xs px-2 py-1 rounded-full ${item.type === 'work' ? 'bg-blue-500/20 text-blue-400' : item.type === 'education' ? 'bg-green-500/20 text-green-400' : 'bg-purple-500/20 text-purple-400'}`,\n                        children: item.type\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 270,\n                        columnNumber: 29\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 266,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                      className: \"text-xl font-bold text-white mb-1\",\n                      children: item.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 277,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-blue-400 mb-3 font-medium\",\n                      children: item.company\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 278,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-gray-300 leading-relaxed\",\n                      children: item.description\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 279,\n                      columnNumber: 27\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 265,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 251,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 15\n          }, this), activeTab === 'values' && /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-3xl font-bold text-white mb-8 text-center\",\n              children: \"What Drives Me\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n              children: values.map((value, index) => {\n                const IconComponent = value.icon;\n                return /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    y: 30\n                  },\n                  animate: {\n                    opacity: 1,\n                    y: 0\n                  },\n                  transition: {\n                    delay: index * 0.2\n                  },\n                  whileHover: {\n                    scale: 1.05,\n                    y: -5\n                  },\n                  className: \"bg-gray-700/30 border border-gray-600 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 group\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(IconComponent, {\n                      className: \"w-6 h-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 304,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors\",\n                    children: value.title\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 306,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-300 leading-relaxed\",\n                    children: value.description\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 25\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 295,\n                  columnNumber: 23\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 291,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 15\n          }, this)]\n        }, activeTab, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-900 mb-8\",\n          children: \"Technologies I Work With\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 326,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap justify-center gap-4\",\n          children: ['React', 'Node.js', 'JavaScript', 'TypeScript', 'Python', 'MongoDB', 'PostgreSQL', 'AWS', 'Docker', 'Git'].map((tech, index) => /*#__PURE__*/_jsxDEV(motion.span, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.3,\n              delay: index * 0.05\n            },\n            className: \"px-6 py-3 bg-gradient-to-r from-blue-50 to-purple-50 text-gray-700 rounded-full font-medium hover:shadow-md transition-shadow duration-300\",\n            children: tech\n          }, tech, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 327,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"text-center mt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold mb-4\",\n            children: \"Ready to work together?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg mb-6 opacity-90\",\n            children: \"Let's create something amazing that makes a difference\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#contact\",\n            className: \"inline-flex items-center px-8 py-3 bg-white text-blue-600 rounded-full font-semibold hover:shadow-lg transition-all duration-300\",\n            children: [\"Get In Touch\", /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 ml-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n};\n_s(About, \"xrVXYcbQxoKv7AZJ0h61LqKtyPU=\");\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "FaGraduationCap", "FaBriefcase", "FaAward", "FaHeart", "FaCode", "FaRocket", "FaUsers", "FaLightbulb", "jsxDEV", "_jsxDEV", "About", "_s", "activeTab", "setActiveTab", "stats", "number", "label", "icon", "color", "timeline", "year", "title", "company", "description", "type", "values", "tabs", "id", "className", "children", "div", "animate", "scale", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "opacity", "y", "whileInView", "span", "delay", "width", "map", "stat", "index", "IconComponent", "whileHover", "tab", "button", "onClick", "whileTap", "layoutId", "bounce", "mode", "exit", "item", "x", "value", "tech", "href", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/About.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGraduationCap, FaBriefcase, FaAward, FaHeart, FaCode, FaRocket, FaUsers, FaLightbulb } from 'react-icons/fa';\n\nconst About = () => {\n  const [activeTab, setActiveTab] = useState('story');\n\n  const stats = [\n    { number: '5+', label: 'Years Experience', icon: FaRocket, color: 'from-blue-500 to-cyan-500' },\n    { number: '50+', label: 'Projects Completed', icon: FaBriefcase, color: 'from-green-500 to-emerald-500' },\n    { number: '15+', label: 'Technologies', icon: FaCode, color: 'from-purple-500 to-pink-500' },\n    { number: '100%', label: 'Client Satisfaction', icon: FaHeart, color: 'from-red-500 to-rose-500' }\n  ];\n\n  const timeline = [\n    {\n      year: '2023',\n      title: 'Senior Full Stack Developer',\n      company: 'Tech Innovations Inc.',\n      description: 'Leading development of enterprise-scale applications, mentoring junior developers, and architecting scalable solutions.',\n      icon: FaBriefcase,\n      type: 'work'\n    },\n    {\n      year: '2022',\n      title: 'AWS Certified Developer',\n      company: 'Amazon Web Services',\n      description: 'Achieved AWS certification demonstrating expertise in cloud architecture and serverless technologies.',\n      icon: FaAward,\n      type: 'achievement'\n    },\n    {\n      year: '2021',\n      title: 'Full Stack Developer',\n      company: 'Digital Solutions Ltd.',\n      description: 'Developed and maintained multiple client projects using React, Node.js, and cloud technologies.',\n      icon: FaBriefcase,\n      type: 'work'\n    },\n    {\n      year: '2020',\n      title: 'Computer Science Degree',\n      company: 'University of Technology',\n      description: 'Graduated with honors, specializing in software engineering and web technologies.',\n      icon: FaGraduationCap,\n      type: 'education'\n    }\n  ];\n\n  const values = [\n    {\n      icon: FaLightbulb,\n      title: 'Innovation',\n      description: 'Always exploring new technologies and creative solutions to complex problems.'\n    },\n    {\n      icon: FaUsers,\n      title: 'Collaboration',\n      description: 'Believing in the power of teamwork and open communication to achieve great results.'\n    },\n    {\n      icon: FaRocket,\n      title: 'Excellence',\n      description: 'Committed to delivering high-quality code and exceptional user experiences.'\n    },\n    {\n      icon: FaHeart,\n      title: 'Passion',\n      description: 'Genuinely passionate about technology and its potential to make a positive impact.'\n    }\n  ];\n\n  const tabs = [\n    { id: 'story', label: 'My Story', icon: FaHeart },\n    { id: 'journey', label: 'Journey', icon: FaRocket },\n    { id: 'values', label: 'Values', icon: FaLightbulb }\n  ];\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            className=\"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            👋 Get to know me\n          </motion.span>\n\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            About\n            <span className=\"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\"> Me</span>\n          </h2>\n\n          <motion.div\n            className=\"w-20 h-1 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto mb-6\"\n            initial={{ width: 0 }}\n            whileInView={{ width: 80 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n          />\n\n          <p className=\"text-xl text-gray-400 max-w-3xl mx-auto\">\n            Passionate developer, creative problem solver, and technology enthusiast dedicated to building exceptional digital experiences\n          </p>\n        </motion.div>\n\n        {/* Stats Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-6 mb-20\"\n        >\n          {stats.map((stat, index) => {\n            const IconComponent = stat.icon;\n            return (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.5 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                whileHover={{ scale: 1.05, y: -5 }}\n                className=\"bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 text-center hover:border-blue-500/50 transition-all duration-300 group\"\n              >\n                <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>\n                  <IconComponent className=\"w-8 h-8 text-white\" />\n                </div>\n                <div className=\"text-3xl font-bold text-white mb-2\">{stat.number}</div>\n                <div className=\"text-sm text-gray-400 font-medium\">{stat.label}</div>\n              </motion.div>\n            );\n          })}\n        </motion.div>\n\n        {/* Tab Navigation */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"flex justify-center mb-12\"\n        >\n          <div className=\"bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-full p-2 shadow-xl\">\n            {tabs.map((tab, index) => {\n              const IconComponent = tab.icon;\n              return (\n                <motion.button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`relative px-6 py-3 rounded-full font-semibold transition-all duration-300 flex items-center gap-2 ${\n                    activeTab === tab.id\n                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'\n                      : 'text-gray-400 hover:text-white hover:bg-gray-700/50'\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <IconComponent className=\"w-4 h-4\" />\n                  {tab.label}\n\n                  {activeTab === tab.id && (\n                    <motion.div\n                      className=\"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full\"\n                      layoutId=\"activeAboutTab\"\n                      transition={{ type: \"spring\", bounce: 0.2, duration: 0.6 }}\n                    />\n                  )}\n                </motion.button>\n              );\n            })}\n          </div>\n        </motion.div>\n\n        {/* Tab Content */}\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={activeTab}\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            transition={{ duration: 0.5 }}\n            className=\"bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-3xl p-8 mb-20\"\n          >\n            {activeTab === 'story' && (\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n                <div>\n                  <h3 className=\"text-3xl font-bold text-white mb-6\">\n                    Full Stack Developer & Creative Problem Solver\n                  </h3>\n\n                  <p className=\"text-lg text-gray-300 mb-6 leading-relaxed\">\n                    I'm a passionate full-stack developer with over 5 years of experience creating digital solutions that make a real difference. My journey in tech started with curiosity about how websites work and has evolved into a career dedicated to building applications that are both beautiful and functional.\n                  </p>\n\n                  <p className=\"text-lg text-gray-300 mb-6 leading-relaxed\">\n                    I specialize in modern web technologies including React, Node.js, and cloud platforms. I believe in writing clean, maintainable code and creating user experiences that delight and inspire. Every project is an opportunity to learn something new and push the boundaries of what's possible.\n                  </p>\n\n                  <p className=\"text-lg text-gray-300 leading-relaxed\">\n                    When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or mentoring aspiring developers. I'm always excited to take on new challenges and collaborate with teams that share my passion for innovation.\n                  </p>\n                </div>\n\n                <div className=\"relative\">\n                  <motion.div\n                    className=\"w-80 h-80 mx-auto bg-gradient-to-br from-gray-800 to-gray-900 rounded-full border-4 border-gray-700 overflow-hidden shadow-2xl\"\n                    whileHover={{ scale: 1.05 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20\" />\n                    <div className=\"absolute inset-0 flex items-center justify-center\">\n                      <div className=\"text-8xl text-gray-600\">👨‍💻</div>\n                    </div>\n                  </motion.div>\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'journey' && (\n              <div>\n                <h3 className=\"text-3xl font-bold text-white mb-8 text-center\">My Professional Journey</h3>\n                <div className=\"space-y-8\">\n                  {timeline.map((item, index) => {\n                    const IconComponent = item.icon;\n                    return (\n                      <motion.div\n                        key={index}\n                        initial={{ opacity: 0, x: -50 }}\n                        animate={{ opacity: 1, x: 0 }}\n                        transition={{ delay: index * 0.2 }}\n                        className=\"flex items-start gap-6 group\"\n                      >\n                        <div className={`w-16 h-16 rounded-full flex items-center justify-center flex-shrink-0 ${\n                          item.type === 'work' ? 'bg-blue-500' :\n                          item.type === 'education' ? 'bg-green-500' : 'bg-purple-500'\n                        } group-hover:scale-110 transition-transform duration-300`}>\n                          <IconComponent className=\"w-8 h-8 text-white\" />\n                        </div>\n\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center gap-4 mb-2\">\n                            <span className=\"text-sm font-bold text-blue-400 bg-blue-500/20 px-3 py-1 rounded-full\">\n                              {item.year}\n                            </span>\n                            <span className={`text-xs px-2 py-1 rounded-full ${\n                              item.type === 'work' ? 'bg-blue-500/20 text-blue-400' :\n                              item.type === 'education' ? 'bg-green-500/20 text-green-400' : 'bg-purple-500/20 text-purple-400'\n                            }`}>\n                              {item.type}\n                            </span>\n                          </div>\n                          <h4 className=\"text-xl font-bold text-white mb-1\">{item.title}</h4>\n                          <p className=\"text-blue-400 mb-3 font-medium\">{item.company}</p>\n                          <p className=\"text-gray-300 leading-relaxed\">{item.description}</p>\n                        </div>\n                      </motion.div>\n                    );\n                  })}\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'values' && (\n              <div>\n                <h3 className=\"text-3xl font-bold text-white mb-8 text-center\">What Drives Me</h3>\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n                  {values.map((value, index) => {\n                    const IconComponent = value.icon;\n                    return (\n                      <motion.div\n                        key={index}\n                        initial={{ opacity: 0, y: 30 }}\n                        animate={{ opacity: 1, y: 0 }}\n                        transition={{ delay: index * 0.2 }}\n                        whileHover={{ scale: 1.05, y: -5 }}\n                        className=\"bg-gray-700/30 border border-gray-600 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 group\"\n                      >\n                        <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\">\n                          <IconComponent className=\"w-6 h-6 text-white\" />\n                        </div>\n                        <h4 className=\"text-xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors\">\n                          {value.title}\n                        </h4>\n                        <p className=\"text-gray-300 leading-relaxed\">{value.description}</p>\n                      </motion.div>\n                    );\n                  })}\n                </div>\n              </div>\n            )}\n          </motion.div>\n        </AnimatePresence>\n\n        {/* Technologies */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">Technologies I Work With</h3>\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {[\n              'React', 'Node.js', 'JavaScript', 'TypeScript', 'Python', \n              'MongoDB', 'PostgreSQL', 'AWS', 'Docker', 'Git'\n            ].map((tech, index) => (\n              <motion.span\n                key={tech}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3, delay: index * 0.05 }}\n                className=\"px-6 py-3 bg-gradient-to-r from-blue-50 to-purple-50 text-gray-700 rounded-full font-medium hover:shadow-md transition-shadow duration-300\"\n              >\n                {tech}\n              </motion.span>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Call to action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\">\n            <h3 className=\"text-2xl font-bold mb-4\">Ready to work together?</h3>\n            <p className=\"text-lg mb-6 opacity-90\">\n              Let's create something amazing that makes a difference\n            </p>\n            <a\n              href=\"#contact\"\n              className=\"inline-flex items-center px-8 py-3 bg-white text-blue-600 rounded-full font-semibold hover:shadow-lg transition-all duration-300\"\n            >\n              Get In Touch\n              <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </a>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,eAAe,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExH,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,OAAO,CAAC;EAEnD,MAAMiB,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAEZ,QAAQ;IAAEa,KAAK,EAAE;EAA4B,CAAC,EAC/F;IAAEH,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,EAAEhB,WAAW;IAAEiB,KAAK,EAAE;EAAgC,CAAC,EACzG;IAAEH,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAEb,MAAM;IAAEc,KAAK,EAAE;EAA8B,CAAC,EAC5F;IAAEH,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAEd,OAAO;IAAEe,KAAK,EAAE;EAA2B,CAAC,CACnG;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,uBAAuB;IAChCC,WAAW,EAAE,yHAAyH;IACtIN,IAAI,EAAEhB,WAAW;IACjBuB,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,qBAAqB;IAC9BC,WAAW,EAAE,uGAAuG;IACpHN,IAAI,EAAEf,OAAO;IACbsB,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,sBAAsB;IAC7BC,OAAO,EAAE,wBAAwB;IACjCC,WAAW,EAAE,iGAAiG;IAC9GN,IAAI,EAAEhB,WAAW;IACjBuB,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,mFAAmF;IAChGN,IAAI,EAAEjB,eAAe;IACrBwB,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,MAAM,GAAG,CACb;IACER,IAAI,EAAEV,WAAW;IACjBc,KAAK,EAAE,YAAY;IACnBE,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAEX,OAAO;IACbe,KAAK,EAAE,eAAe;IACtBE,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAEZ,QAAQ;IACdgB,KAAK,EAAE,YAAY;IACnBE,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAEd,OAAO;IACbkB,KAAK,EAAE,SAAS;IAChBE,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMG,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,OAAO;IAAEX,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAEd;EAAQ,CAAC,EACjD;IAAEwB,EAAE,EAAE,SAAS;IAAEX,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEZ;EAAS,CAAC,EACnD;IAAEsB,EAAE,EAAE,QAAQ;IAAEX,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAEV;EAAY,CAAC,CACrD;EAED,oBACEE,OAAA;IAASkB,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,6FAA6F;IAAAC,QAAA,gBAEzHpB,OAAA;MAAKmB,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/CpB,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTF,SAAS,EAAC,iHAAiH;QAC3HG,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENjC,OAAA;MAAKmB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEpB,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BP,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BpB,OAAA,CAACX,MAAM,CAACiD,IAAI;UACVnB,SAAS,EAAC,yJAAyJ;UACnKe,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE;UAAI,CAAE;UACpCc,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE;UAAE,CAAE;UACtCE,UAAU,EAAE;YAAEc,KAAK,EAAE;UAAI,CAAE;UAAAnB,QAAA,EAC5B;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAEdjC,OAAA;UAAImB,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAAC,OAE7D,eAAApB,OAAA;YAAMmB,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAAG;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG,CAAC,eAELjC,OAAA,CAACX,MAAM,CAACgC,GAAG;UACTF,SAAS,EAAC,oEAAoE;UAC9Ee,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBH,WAAW,EAAE;YAAEG,KAAK,EAAE;UAAG,CAAE;UAC3Bf,UAAU,EAAE;YAAEc,KAAK,EAAE,GAAG;YAAEb,QAAQ,EAAE;UAAI;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAEFjC,OAAA;UAAGmB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbjC,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEa,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,EAEtDf,KAAK,CAACoC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;UAC1B,MAAMC,aAAa,GAAGF,IAAI,CAAClC,IAAI;UAC/B,oBACER,OAAA,CAACX,MAAM,CAACgC,GAAG;YAETa,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAI,CAAE;YACpCc,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAE,CAAE;YACtCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEa,KAAK,EAAEI,KAAK,GAAG;YAAI,CAAE;YAClDE,UAAU,EAAE;cAAEtB,KAAK,EAAE,IAAI;cAAEa,CAAC,EAAE,CAAC;YAAE,CAAE;YACnCjB,SAAS,EAAC,+IAA+I;YAAAC,QAAA,gBAEzJpB,OAAA;cAAKmB,SAAS,EAAE,8BAA8BuB,IAAI,CAACjC,KAAK,mHAAoH;cAAAW,QAAA,eAC1KpB,OAAA,CAAC4C,aAAa;gBAACzB,SAAS,EAAC;cAAoB;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC,eACNjC,OAAA;cAAKmB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAAEsB,IAAI,CAACpC;YAAM;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEjC,OAAA;cAAKmB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAEsB,IAAI,CAACnC;YAAK;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAXhES,IAAI,CAACnC,KAAK;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAYL,CAAC;QAEjB,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGbjC,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEa,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eAErCpB,OAAA;UAAKmB,SAAS,EAAC,mFAAmF;UAAAC,QAAA,EAC/FH,IAAI,CAACwB,GAAG,CAAC,CAACK,GAAG,EAAEH,KAAK,KAAK;YACxB,MAAMC,aAAa,GAAGE,GAAG,CAACtC,IAAI;YAC9B,oBACER,OAAA,CAACX,MAAM,CAAC0D,MAAM;cAEZC,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAAC0C,GAAG,CAAC5B,EAAE,CAAE;cACpCC,SAAS,EAAE,qGACThB,SAAS,KAAK2C,GAAG,CAAC5B,EAAE,GAChB,mEAAmE,GACnE,qDAAqD,EACxD;cACH2B,UAAU,EAAE;gBAAEtB,KAAK,EAAE;cAAK,CAAE;cAC5B0B,QAAQ,EAAE;gBAAE1B,KAAK,EAAE;cAAK,CAAE;cAC1BW,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAG,CAAE;cAC/Bd,OAAO,EAAE;gBAAEa,OAAO,EAAE,CAAC;gBAAEC,CAAC,EAAE;cAAE,CAAE;cAC9BX,UAAU,EAAE;gBAAEc,KAAK,EAAEI,KAAK,GAAG;cAAI,CAAE;cAAAvB,QAAA,gBAEnCpB,OAAA,CAAC4C,aAAa;gBAACzB,SAAS,EAAC;cAAS;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EACpCa,GAAG,CAACvC,KAAK,EAETJ,SAAS,KAAK2C,GAAG,CAAC5B,EAAE,iBACnBlB,OAAA,CAACX,MAAM,CAACgC,GAAG;gBACTF,SAAS,EAAC,kFAAkF;gBAC5F+B,QAAQ,EAAC,gBAAgB;gBACzBzB,UAAU,EAAE;kBAAEV,IAAI,EAAE,QAAQ;kBAAEoC,MAAM,EAAE,GAAG;kBAAEzB,QAAQ,EAAE;gBAAI;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CACF;YAAA,GAtBIa,GAAG,CAAC5B,EAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAuBE,CAAC;UAEpB,CAAC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbjC,OAAA,CAACV,eAAe;QAAC8D,IAAI,EAAC,MAAM;QAAAhC,QAAA,eAC1BpB,OAAA,CAACX,MAAM,CAACgC,GAAG;UAETa,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/Bd,OAAO,EAAE;YAAEa,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BiB,IAAI,EAAE;YAAElB,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BX,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,8EAA8E;UAAAC,QAAA,GAEvFjB,SAAS,KAAK,OAAO,iBACpBH,OAAA;YAAKmB,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAClEpB,OAAA;cAAAoB,QAAA,gBACEpB,OAAA;gBAAImB,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAAC;cAEnD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAELjC,OAAA;gBAAGmB,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAEJjC,OAAA;gBAAGmB,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EAAC;cAE1D;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAEJjC,OAAA;gBAAGmB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAC;cAErD;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENjC,OAAA;cAAKmB,SAAS,EAAC,UAAU;cAAAC,QAAA,eACvBpB,OAAA,CAACX,MAAM,CAACgC,GAAG;gBACTF,SAAS,EAAC,gIAAgI;gBAC1I0B,UAAU,EAAE;kBAAEtB,KAAK,EAAE;gBAAK,CAAE;gBAC5BE,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAN,QAAA,gBAE9BpB,OAAA;kBAAKmB,SAAS,EAAC;gBAAsE;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxFjC,OAAA;kBAAKmB,SAAS,EAAC,mDAAmD;kBAAAC,QAAA,eAChEpB,OAAA;oBAAKmB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,EAAC;kBAAK;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA9B,SAAS,KAAK,SAAS,iBACtBH,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAImB,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAC;YAAuB;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3FjC,OAAA;cAAKmB,SAAS,EAAC,WAAW;cAAAC,QAAA,EACvBV,QAAQ,CAAC+B,GAAG,CAAC,CAACa,IAAI,EAAEX,KAAK,KAAK;gBAC7B,MAAMC,aAAa,GAAGU,IAAI,CAAC9C,IAAI;gBAC/B,oBACER,OAAA,CAACX,MAAM,CAACgC,GAAG;kBAETa,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEoB,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCjC,OAAO,EAAE;oBAAEa,OAAO,EAAE,CAAC;oBAAEoB,CAAC,EAAE;kBAAE,CAAE;kBAC9B9B,UAAU,EAAE;oBAAEc,KAAK,EAAEI,KAAK,GAAG;kBAAI,CAAE;kBACnCxB,SAAS,EAAC,8BAA8B;kBAAAC,QAAA,gBAExCpB,OAAA;oBAAKmB,SAAS,EAAE,yEACdmC,IAAI,CAACvC,IAAI,KAAK,MAAM,GAAG,aAAa,GACpCuC,IAAI,CAACvC,IAAI,KAAK,WAAW,GAAG,cAAc,GAAG,eAAe,0DACH;oBAAAK,QAAA,eACzDpB,OAAA,CAAC4C,aAAa;sBAACzB,SAAS,EAAC;oBAAoB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eAENjC,OAAA;oBAAKmB,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBpB,OAAA;sBAAKmB,SAAS,EAAC,8BAA8B;sBAAAC,QAAA,gBAC3CpB,OAAA;wBAAMmB,SAAS,EAAC,uEAAuE;wBAAAC,QAAA,EACpFkC,IAAI,CAAC3C;sBAAI;wBAAAmB,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eACPjC,OAAA;wBAAMmB,SAAS,EAAE,kCACfmC,IAAI,CAACvC,IAAI,KAAK,MAAM,GAAG,8BAA8B,GACrDuC,IAAI,CAACvC,IAAI,KAAK,WAAW,GAAG,gCAAgC,GAAG,kCAAkC,EAChG;wBAAAK,QAAA,EACAkC,IAAI,CAACvC;sBAAI;wBAAAe,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ,CAAC,eACNjC,OAAA;sBAAImB,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEkC,IAAI,CAAC1C;oBAAK;sBAAAkB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC,eACnEjC,OAAA;sBAAGmB,SAAS,EAAC,gCAAgC;sBAAAC,QAAA,EAAEkC,IAAI,CAACzC;oBAAO;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eAChEjC,OAAA;sBAAGmB,SAAS,EAAC,+BAA+B;sBAAAC,QAAA,EAAEkC,IAAI,CAACxC;oBAAW;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChE,CAAC;gBAAA,GA5BDU,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA6BA,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN,EAEA9B,SAAS,KAAK,QAAQ,iBACrBH,OAAA;YAAAoB,QAAA,gBACEpB,OAAA;cAAImB,SAAS,EAAC,gDAAgD;cAAAC,QAAA,EAAC;YAAc;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAClFjC,OAAA;cAAKmB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnDJ,MAAM,CAACyB,GAAG,CAAC,CAACe,KAAK,EAAEb,KAAK,KAAK;gBAC5B,MAAMC,aAAa,GAAGY,KAAK,CAAChD,IAAI;gBAChC,oBACER,OAAA,CAACX,MAAM,CAACgC,GAAG;kBAETa,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAG,CAAE;kBAC/Bd,OAAO,EAAE;oBAAEa,OAAO,EAAE,CAAC;oBAAEC,CAAC,EAAE;kBAAE,CAAE;kBAC9BX,UAAU,EAAE;oBAAEc,KAAK,EAAEI,KAAK,GAAG;kBAAI,CAAE;kBACnCE,UAAU,EAAE;oBAAEtB,KAAK,EAAE,IAAI;oBAAEa,CAAC,EAAE,CAAC;kBAAE,CAAE;kBACnCjB,SAAS,EAAC,iHAAiH;kBAAAC,QAAA,gBAE3HpB,OAAA;oBAAKmB,SAAS,EAAC,iKAAiK;oBAAAC,QAAA,eAC9KpB,OAAA,CAAC4C,aAAa;sBAACzB,SAAS,EAAC;oBAAoB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C,CAAC,eACNjC,OAAA;oBAAImB,SAAS,EAAC,+EAA+E;oBAAAC,QAAA,EAC1FoC,KAAK,CAAC5C;kBAAK;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV,CAAC,eACLjC,OAAA;oBAAGmB,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAAEoC,KAAK,CAAC1C;kBAAW;oBAAAgB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA,GAb/DU,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAcA,CAAC;cAEjB,CAAC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA,GAjHI9B,SAAS;UAAA2B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkHJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGlBjC,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BP,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBpB,OAAA;UAAImB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAwB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnFjC,OAAA;UAAKmB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjD,CACC,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EACxD,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,CAChD,CAACqB,GAAG,CAAC,CAACgB,IAAI,EAAEd,KAAK,kBAChB3C,OAAA,CAACX,MAAM,CAACiD,IAAI;YAEVJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAI,CAAE;YACpCc,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAE,CAAE;YACtCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEa,KAAK,EAAEI,KAAK,GAAG;YAAK,CAAE;YACnDxB,SAAS,EAAC,4IAA4I;YAAAC,QAAA,EAErJqC;UAAI,GANAA,IAAI;YAAA3B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOE,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbjC,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEa,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAE7BpB,OAAA;UAAKmB,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFpB,OAAA;YAAImB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAuB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEjC,OAAA;YAAGmB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJjC,OAAA;YACE0D,IAAI,EAAC,UAAU;YACfvC,SAAS,EAAC,kIAAkI;YAAAC,QAAA,GAC7I,cAEC,eAAApB,OAAA;cAAKmB,SAAS,EAAC,cAAc;cAACwC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAzC,QAAA,eACjFpB,OAAA;gBAAM8D,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA0B;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC/B,EAAA,CA9WID,KAAK;AAAAiE,EAAA,GAALjE,KAAK;AAgXX,eAAeA,KAAK;AAAC,IAAAiE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}