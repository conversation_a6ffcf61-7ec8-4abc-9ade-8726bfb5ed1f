[{"C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Navbar.js": "3", "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\About.js": "4", "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Contact.js": "5", "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Projects.js": "6", "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Skills.js": "7", "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Hero.js": "8", "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Footer.js": "9", "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\LoadingScreen.js": "10"}, {"size": 254, "mtime": 1753183574388, "results": "11", "hashOfConfig": "12"}, {"size": 3269, "mtime": 1753188963166, "results": "13", "hashOfConfig": "12"}, {"size": 6354, "mtime": 1753189006868, "results": "14", "hashOfConfig": "12"}, {"size": 19470, "mtime": 1753193306465, "results": "15", "hashOfConfig": "12"}, {"size": 12995, "mtime": 1753193390920, "results": "16", "hashOfConfig": "12"}, {"size": 19706, "mtime": 1753193126007, "results": "17", "hashOfConfig": "12"}, {"size": 19319, "mtime": 1753192902572, "results": "18", "hashOfConfig": "12"}, {"size": 11549, "mtime": 1753192731533, "results": "19", "hashOfConfig": "12"}, {"size": 8633, "mtime": 1753190539861, "results": "20", "hashOfConfig": "12"}, {"size": 6674, "mtime": 1753186350687, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "4ony8j", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Navbar.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\About.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Contact.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Projects.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Skills.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Hero.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\Footer.js", [], [], "C:\\Users\\<USER>\\Desktop\\Portfolio my\\src\\components\\LoadingScreen.js", [], []]