{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\Hero.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaGithub, FaLinkedin, FaTwitter, FaDownload, FaArrowRight, FaCode, FaPalette, FaRocket } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  _s();\n  const [currentRole, setCurrentRole] = useState(0);\n  const [text, setText] = useState('');\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [loopNum, setLoopNum] = useState(0);\n  const [typingSpeed, setTypingSpeed] = useState(150);\n  const roles = ['Full Stack Developer', 'Frontend Specialist', 'UI/UX Designer', 'React Expert', 'Problem Solver'];\n\n  // Typewriter effect\n  useEffect(() => {\n    const handleTyping = () => {\n      const i = loopNum % roles.length;\n      const fullText = roles[i];\n      setText(isDeleting ? fullText.substring(0, text.length - 1) : fullText.substring(0, text.length + 1));\n      setTypingSpeed(isDeleting ? 30 : 150);\n      if (!isDeleting && text === fullText) {\n        setTimeout(() => setIsDeleting(true), 500);\n      } else if (isDeleting && text === '') {\n        setIsDeleting(false);\n        setLoopNum(loopNum + 1);\n      }\n    };\n    const timer = setTimeout(handleTyping, typingSpeed);\n    return () => clearTimeout(timer);\n  }, [text, isDeleting, loopNum, typingSpeed, roles]);\n\n  // Simple role rotation for fallback\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentRole(prev => (prev + 1) % roles.length);\n    }, 3000);\n    return () => clearInterval(interval);\n  }, [roles.length]);\n  const scrollToSection = sectionId => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById(sectionId)) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"home\",\n    className: \"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-full blur-3xl\",\n        animate: {\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full blur-3xl\",\n        animate: {\n          scale: [1.2, 1, 1.2],\n          rotate: [360, 180, 0]\n        },\n        transition: {\n          duration: 25,\n          repeat: Infinity,\n          ease: \"linear\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), [...Array(30)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-1 h-1 bg-white/20 rounded-full\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`\n        },\n        animate: {\n          y: [0, -100, 0],\n          opacity: [0, 1, 0]\n        },\n        transition: {\n          duration: Math.random() * 10 + 10,\n          repeat: Infinity,\n          delay: Math.random() * 10\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container mx-auto px-6 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid lg:grid-cols-2 gap-12 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -50\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.8\n          },\n          className: \"text-left\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.2,\n              duration: 0.8\n            },\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4\",\n              children: \"\\uD83D\\uDC4B Welcome to my portfolio\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight\",\n              children: [\"Hi, I'm\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent\",\n                children: \"Alex Johnson\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-2xl md:text-3xl lg:text-4xl font-semibold text-gray-300 mb-8 h-12\",\n              children: [\"I'm a\", ' ', /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n                children: [text, /*#__PURE__*/_jsxDEV(motion.span, {\n                  animate: {\n                    opacity: [1, 0]\n                  },\n                  transition: {\n                    duration: 0.5,\n                    repeat: Infinity\n                  },\n                  className: \"text-blue-400\",\n                  children: \"|\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 136,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n            className: \"text-lg md:text-xl text-gray-400 mb-8 max-w-2xl leading-relaxed\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 0.6,\n              duration: 0.8\n            },\n            children: \"I craft exceptional digital experiences with clean code and beautiful design. Passionate about turning complex problems into simple, elegant solutions that users love.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"flex flex-col sm:flex-row gap-4 mb-8\",\n            initial: {\n              opacity: 0,\n              y: 30\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1,\n              duration: 0.8\n            },\n            children: [/*#__PURE__*/_jsxDEV(motion.button, {\n              onClick: () => scrollToSection('projects'),\n              className: \"group px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\",\n              whileHover: {\n                scale: 1.05,\n                y: -2\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [\"View My Work\", /*#__PURE__*/_jsxDEV(FaArrowRight, {\n                className: \"group-hover:translate-x-1 transition-transform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 165,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n              className: \"group px-8 py-4 border-2 border-gray-600 text-gray-300 font-semibold rounded-full hover:border-blue-500 hover:text-blue-400 transition-all duration-300 flex items-center justify-center gap-2\",\n              whileHover: {\n                scale: 1.05,\n                y: -2\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [/*#__PURE__*/_jsxDEV(FaDownload, {\n                className: \"group-hover:animate-bounce\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), \"Download CV\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"flex gap-4\",\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: 1.4,\n              duration: 0.8\n            },\n            children: [{\n              icon: FaGithub,\n              href: '#',\n              label: 'GitHub'\n            }, {\n              icon: FaLinkedin,\n              href: '#',\n              label: 'LinkedIn'\n            }, {\n              icon: FaTwitter,\n              href: '#',\n              label: 'Twitter'\n            }].map(({\n              icon: Icon,\n              href,\n              label\n            }) => /*#__PURE__*/_jsxDEV(motion.a, {\n              href: href,\n              className: \"w-12 h-12 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:border-blue-500 hover:bg-blue-500/10 transition-all duration-300\",\n              whileHover: {\n                scale: 1.1,\n                y: -2\n              },\n              whileTap: {\n                scale: 0.9\n              },\n              \"aria-label\": label,\n              children: /*#__PURE__*/_jsxDEV(Icon, {\n                size: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 205,\n                columnNumber: 19\n              }, this)\n            }, label, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"relative\",\n          initial: {\n            opacity: 0,\n            x: 50\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative w-full max-w-lg mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-80 h-80 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto shadow-2xl\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-8xl\",\n                  children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), [{\n                name: 'React',\n                color: 'bg-blue-500',\n                position: 'top-4 -left-4'\n              }, {\n                name: 'Node.js',\n                color: 'bg-green-500',\n                position: 'top-16 -right-8'\n              }, {\n                name: 'JavaScript',\n                color: 'bg-yellow-500',\n                position: 'bottom-16 -left-8'\n              }, {\n                name: 'CSS',\n                color: 'bg-purple-500',\n                position: 'bottom-4 -right-4'\n              }].map((tech, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                className: `absolute ${tech.position} ${tech.color} text-white px-4 py-2 rounded-lg text-sm font-semibold shadow-lg`,\n                animate: {\n                  y: [0, -10, 0],\n                  rotate: [0, 5, 0]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  delay: index * 0.5\n                },\n                children: tech.name\n              }, tech.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n        animate: {\n          y: [0, 10, 0]\n        },\n        transition: {\n          duration: 2,\n          repeat: Infinity\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-1 h-3 bg-gray-400 rounded-full mt-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 259,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 254,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(Hero, \"hcvPJgWEzymSzgcsGRbD3VU17to=\");\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaLinkedin", "FaTwitter", "FaDownload", "FaArrowRight", "FaCode", "FaPalette", "FaRocket", "jsxDEV", "_jsxDEV", "Hero", "_s", "currentRole", "setCurrentRole", "text", "setText", "isDeleting", "setIsDeleting", "loopNum", "setLoopNum", "typingSpeed", "setTypingSpeed", "roles", "handleTyping", "i", "length", "fullText", "substring", "setTimeout", "timer", "clearTimeout", "interval", "setInterval", "prev", "clearInterval", "scrollToSection", "sectionId", "_document$getElementB", "document", "getElementById", "scrollIntoView", "behavior", "id", "className", "children", "div", "animate", "scale", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "style", "left", "Math", "random", "top", "y", "opacity", "delay", "initial", "x", "span", "p", "button", "onClick", "whileHover", "whileTap", "icon", "href", "label", "Icon", "a", "size", "name", "color", "position", "tech", "index", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/Hero.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaGithub, FaLinkedin, FaTwitter, FaDownload, FaArrowRight, FaCode, FaPalette, FaRocket } from 'react-icons/fa';\n\nconst Hero = () => {\n  const [currentRole, setCurrentRole] = useState(0);\n  const [text, setText] = useState('');\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [loopNum, setLoopNum] = useState(0);\n  const [typingSpeed, setTypingSpeed] = useState(150);\n\n  const roles = [\n    'Full Stack Developer',\n    'Frontend Specialist',\n    'UI/UX Designer',\n    'React Expert',\n    'Problem Solver'\n  ];\n\n  // Typewriter effect\n  useEffect(() => {\n    const handleTyping = () => {\n      const i = loopNum % roles.length;\n      const fullText = roles[i];\n\n      setText(isDeleting\n        ? fullText.substring(0, text.length - 1)\n        : fullText.substring(0, text.length + 1)\n      );\n\n      setTypingSpeed(isDeleting ? 30 : 150);\n\n      if (!isDeleting && text === fullText) {\n        setTimeout(() => setIsDeleting(true), 500);\n      } else if (isDeleting && text === '') {\n        setIsDeleting(false);\n        setLoopNum(loopNum + 1);\n      }\n    };\n\n    const timer = setTimeout(handleTyping, typingSpeed);\n    return () => clearTimeout(timer);\n  }, [text, isDeleting, loopNum, typingSpeed, roles]);\n\n  // Simple role rotation for fallback\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentRole((prev) => (prev + 1) % roles.length);\n    }, 3000);\n    return () => clearInterval(interval);\n  }, [roles.length]);\n\n  const scrollToSection = (sectionId) => {\n    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <section id=\"home\" className=\"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n        <motion.div\n          className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full blur-3xl\"\n          animate={{\n            scale: [1.2, 1, 1.2],\n            rotate: [360, 180, 0],\n          }}\n          transition={{\n            duration: 25,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n\n        {/* Floating particles */}\n        {[...Array(30)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-white/20 rounded-full\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              y: [0, -100, 0],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: Math.random() * 10 + 10,\n              repeat: Infinity,\n              delay: Math.random() * 10,\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"container mx-auto px-6 relative z-10\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Left side - Text content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8 }}\n            className=\"text-left\"\n          >\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2, duration: 0.8 }}\n              className=\"mb-6\"\n            >\n              <span className=\"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4\">\n                👋 Welcome to my portfolio\n              </span>\n\n              <h1 className=\"text-4xl md:text-6xl lg:text-7xl font-bold text-white mb-6 leading-tight\">\n                Hi, I'm{' '}\n                <span className=\"bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent\">\n                  Alex Johnson\n                </span>\n              </h1>\n\n              <div className=\"text-2xl md:text-3xl lg:text-4xl font-semibold text-gray-300 mb-8 h-12\">\n                I'm a{' '}\n                <span className=\"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\">\n                  {text}\n                  <motion.span\n                    animate={{ opacity: [1, 0] }}\n                    transition={{ duration: 0.5, repeat: Infinity }}\n                    className=\"text-blue-400\"\n                  >\n                    |\n                  </motion.span>\n                </span>\n              </div>\n            </motion.div>\n\n            <motion.p\n              className=\"text-lg md:text-xl text-gray-400 mb-8 max-w-2xl leading-relaxed\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.6, duration: 0.8 }}\n            >\n              I craft exceptional digital experiences with clean code and beautiful design.\n              Passionate about turning complex problems into simple, elegant solutions that users love.\n            </motion.p>\n\n            <motion.div\n              className=\"flex flex-col sm:flex-row gap-4 mb-8\"\n              initial={{ opacity: 0, y: 30 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1, duration: 0.8 }}\n            >\n              <motion.button\n                onClick={() => scrollToSection('projects')}\n                className=\"group px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center gap-2\"\n                whileHover={{ scale: 1.05, y: -2 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                View My Work\n                <FaArrowRight className=\"group-hover:translate-x-1 transition-transform\" />\n              </motion.button>\n\n              <motion.button\n                className=\"group px-8 py-4 border-2 border-gray-600 text-gray-300 font-semibold rounded-full hover:border-blue-500 hover:text-blue-400 transition-all duration-300 flex items-center justify-center gap-2\"\n                whileHover={{ scale: 1.05, y: -2 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <FaDownload className=\"group-hover:animate-bounce\" />\n                Download CV\n              </motion.button>\n            </motion.div>\n\n            {/* Social links */}\n            <motion.div\n              className=\"flex gap-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 1.4, duration: 0.8 }}\n            >\n              {[\n                { icon: FaGithub, href: '#', label: 'GitHub' },\n                { icon: FaLinkedin, href: '#', label: 'LinkedIn' },\n                { icon: FaTwitter, href: '#', label: 'Twitter' }\n              ].map(({ icon: Icon, href, label }) => (\n                <motion.a\n                  key={label}\n                  href={href}\n                  className=\"w-12 h-12 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-full flex items-center justify-center text-gray-400 hover:text-white hover:border-blue-500 hover:bg-blue-500/10 transition-all duration-300\"\n                  whileHover={{ scale: 1.1, y: -2 }}\n                  whileTap={{ scale: 0.9 }}\n                  aria-label={label}\n                >\n                  <Icon size={20} />\n                </motion.a>\n              ))}\n            </motion.div>\n          </motion.div>\n\n          {/* Right side - Visual */}\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <div className=\"relative w-full max-w-lg mx-auto\">\n              {/* Main image container */}\n              <div className=\"relative\">\n                <div className=\"w-80 h-80 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto shadow-2xl\">\n                  <div className=\"text-8xl\">👨‍💻</div>\n                </div>\n                \n                {/* Floating elements */}\n                {[\n                  { name: 'React', color: 'bg-blue-500', position: 'top-4 -left-4' },\n                  { name: 'Node.js', color: 'bg-green-500', position: 'top-16 -right-8' },\n                  { name: 'JavaScript', color: 'bg-yellow-500', position: 'bottom-16 -left-8' },\n                  { name: 'CSS', color: 'bg-purple-500', position: 'bottom-4 -right-4' }\n                ].map((tech, index) => (\n                  <motion.div\n                    key={tech.name}\n                    className={`absolute ${tech.position} ${tech.color} text-white px-4 py-2 rounded-lg text-sm font-semibold shadow-lg`}\n                    animate={{\n                      y: [0, -10, 0],\n                      rotate: [0, 5, 0]\n                    }}\n                    transition={{\n                      duration: 3,\n                      repeat: Infinity,\n                      delay: index * 0.5\n                    }}\n                  >\n                    {tech.name}\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Scroll indicator */}\n        <motion.div\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <div className=\"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-gray-400 rounded-full mt-2\"></div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,UAAU,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExH,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgB,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACzC,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,GAAG,CAAC;EAEnD,MAAMwB,KAAK,GAAG,CACZ,sBAAsB,EACtB,qBAAqB,EACrB,gBAAgB,EAChB,cAAc,EACd,gBAAgB,CACjB;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM0B,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,CAAC,GAAGN,OAAO,GAAGI,KAAK,CAACG,MAAM;MAChC,MAAMC,QAAQ,GAAGJ,KAAK,CAACE,CAAC,CAAC;MAEzBT,OAAO,CAACC,UAAU,GACdU,QAAQ,CAACC,SAAS,CAAC,CAAC,EAAEb,IAAI,CAACW,MAAM,GAAG,CAAC,CAAC,GACtCC,QAAQ,CAACC,SAAS,CAAC,CAAC,EAAEb,IAAI,CAACW,MAAM,GAAG,CAAC,CACzC,CAAC;MAEDJ,cAAc,CAACL,UAAU,GAAG,EAAE,GAAG,GAAG,CAAC;MAErC,IAAI,CAACA,UAAU,IAAIF,IAAI,KAAKY,QAAQ,EAAE;QACpCE,UAAU,CAAC,MAAMX,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;MAC5C,CAAC,MAAM,IAAID,UAAU,IAAIF,IAAI,KAAK,EAAE,EAAE;QACpCG,aAAa,CAAC,KAAK,CAAC;QACpBE,UAAU,CAACD,OAAO,GAAG,CAAC,CAAC;MACzB;IACF,CAAC;IAED,MAAMW,KAAK,GAAGD,UAAU,CAACL,YAAY,EAAEH,WAAW,CAAC;IACnD,OAAO,MAAMU,YAAY,CAACD,KAAK,CAAC;EAClC,CAAC,EAAE,CAACf,IAAI,EAAEE,UAAU,EAAEE,OAAO,EAAEE,WAAW,EAAEE,KAAK,CAAC,CAAC;;EAEnD;EACAzB,SAAS,CAAC,MAAM;IACd,MAAMkC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCnB,cAAc,CAAEoB,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIX,KAAK,CAACG,MAAM,CAAC;IACrD,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMS,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACT,KAAK,CAACG,MAAM,CAAC,CAAC;EAElB,MAAMU,eAAe,GAAIC,SAAS,IAAK;IAAA,IAAAC,qBAAA;IACrC,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC,cAAAC,qBAAA,uBAAlCA,qBAAA,CAAoCG,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC5E,CAAC;EAED,oBACEhC,OAAA;IAASiC,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,qIAAqI;IAAAC,QAAA,gBAEhKnC,OAAA;MAAKkC,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CnC,OAAA,CAACV,MAAM,CAAC8C,GAAG;QACTF,SAAS,EAAC,8GAA8G;QACxHG,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;QACTF,SAAS,EAAC,kHAAkH;QAC5HG,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;UACpBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGD,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEpC,CAAC,kBACvBf,OAAA,CAACV,MAAM,CAAC8C,GAAG;QAETF,SAAS,EAAC,2CAA2C;QACrDkB,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QAC7B,CAAE;QACFlB,OAAO,EAAE;UACPoB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;UACfC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACnB,CAAE;QACFlB,UAAU,EAAE;UACVC,QAAQ,EAAEa,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;UACjCb,MAAM,EAAEC,QAAQ;UAChBgB,KAAK,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QACzB;MAAE,GAdGxC,CAAC;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeP,CACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENhD,OAAA;MAAKkC,SAAS,EAAC,sCAAsC;MAAAC,QAAA,gBACnDnC,OAAA;QAAKkC,SAAS,EAAC,yCAAyC;QAAAC,QAAA,gBAEtDnC,OAAA,CAACV,MAAM,CAAC8C,GAAG;UACTwB,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEG,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCxB,OAAO,EAAE;YAAEqB,OAAO,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAC9BrB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,WAAW;UAAAC,QAAA,gBAErBnC,OAAA,CAACV,MAAM,CAAC8C,GAAG;YACTwB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BpB,OAAO,EAAE;cAAEqB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEmB,KAAK,EAAE,GAAG;cAAElB,QAAQ,EAAE;YAAI,CAAE;YAC1CP,SAAS,EAAC,MAAM;YAAAC,QAAA,gBAEhBnC,OAAA;cAAMkC,SAAS,EAAC,yJAAyJ;cAAAC,QAAA,EAAC;YAE1K;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAEPhD,OAAA;cAAIkC,SAAS,EAAC,0EAA0E;cAAAC,QAAA,GAAC,SAChF,EAAC,GAAG,eACXnC,OAAA;gBAAMkC,SAAS,EAAC,yFAAyF;gBAAAC,QAAA,EAAC;cAE1G;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eAELhD,OAAA;cAAKkC,SAAS,EAAC,wEAAwE;cAAAC,QAAA,GAAC,OACjF,EAAC,GAAG,eACTnC,OAAA;gBAAMkC,SAAS,EAAC,4EAA4E;gBAAAC,QAAA,GACzF9B,IAAI,eACLL,OAAA,CAACV,MAAM,CAACwE,IAAI;kBACVzB,OAAO,EAAE;oBAAEqB,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC;kBAAE,CAAE;kBAC7BlB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAChDT,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAC1B;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAa,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC,eAEbhD,OAAA,CAACV,MAAM,CAACyE,CAAC;YACP7B,SAAS,EAAC,iEAAiE;YAC3E0B,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BpB,OAAO,EAAE;cAAEqB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEmB,KAAK,EAAE,GAAG;cAAElB,QAAQ,EAAE;YAAI,CAAE;YAAAN,QAAA,EAC3C;UAGD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAU,CAAC,eAEXhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;YACTF,SAAS,EAAC,sCAAsC;YAChD0B,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BpB,OAAO,EAAE;cAAEqB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEmB,KAAK,EAAE,CAAC;cAAElB,QAAQ,EAAE;YAAI,CAAE;YAAAN,QAAA,gBAExCnC,OAAA,CAACV,MAAM,CAAC0E,MAAM;cACZC,OAAO,EAAEA,CAAA,KAAMvC,eAAe,CAAC,UAAU,CAAE;cAC3CQ,SAAS,EAAC,iMAAiM;cAC3MgC,UAAU,EAAE;gBAAE5B,KAAK,EAAE,IAAI;gBAAEmB,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCU,QAAQ,EAAE;gBAAE7B,KAAK,EAAE;cAAK,CAAE;cAAAH,QAAA,GAC3B,cAEC,eAAAnC,OAAA,CAACL,YAAY;gBAACuC,SAAS,EAAC;cAAgD;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC,eAEhBhD,OAAA,CAACV,MAAM,CAAC0E,MAAM;cACZ9B,SAAS,EAAC,gMAAgM;cAC1MgC,UAAU,EAAE;gBAAE5B,KAAK,EAAE,IAAI;gBAAEmB,CAAC,EAAE,CAAC;cAAE,CAAE;cACnCU,QAAQ,EAAE;gBAAE7B,KAAK,EAAE;cAAK,CAAE;cAAAH,QAAA,gBAE1BnC,OAAA,CAACN,UAAU;gBAACwC,SAAS,EAAC;cAA4B;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAEvD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAe,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGbhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;YACTF,SAAS,EAAC,YAAY;YACtB0B,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BpB,OAAO,EAAE;cAAEqB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEmB,KAAK,EAAE,GAAG;cAAElB,QAAQ,EAAE;YAAI,CAAE;YAAAN,QAAA,EAEzC,CACC;cAAEiC,IAAI,EAAE7E,QAAQ;cAAE8E,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAS,CAAC,EAC9C;cAAEF,IAAI,EAAE5E,UAAU;cAAE6E,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAW,CAAC,EAClD;cAAEF,IAAI,EAAE3E,SAAS;cAAE4E,IAAI,EAAE,GAAG;cAAEC,KAAK,EAAE;YAAU,CAAC,CACjD,CAACpB,GAAG,CAAC,CAAC;cAAEkB,IAAI,EAAEG,IAAI;cAAEF,IAAI;cAAEC;YAAM,CAAC,kBAChCtE,OAAA,CAACV,MAAM,CAACkF,CAAC;cAEPH,IAAI,EAAEA,IAAK;cACXnC,SAAS,EAAC,sNAAsN;cAChOgC,UAAU,EAAE;gBAAE5B,KAAK,EAAE,GAAG;gBAAEmB,CAAC,EAAE,CAAC;cAAE,CAAE;cAClCU,QAAQ,EAAE;gBAAE7B,KAAK,EAAE;cAAI,CAAE;cACzB,cAAYgC,KAAM;cAAAnC,QAAA,eAElBnC,OAAA,CAACuE,IAAI;gBAACE,IAAI,EAAE;cAAG;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC,GAPbsB,KAAK;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQF,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGbhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;UACTF,SAAS,EAAC,UAAU;UACpB0B,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAG,CAAE;UAC/BxB,OAAO,EAAE;YAAEqB,OAAO,EAAE,CAAC;YAAEG,CAAC,EAAE;UAAE,CAAE;UAC9BrB,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEkB,KAAK,EAAE;UAAI,CAAE;UAAAxB,QAAA,eAE1CnC,OAAA;YAAKkC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,eAE/CnC,OAAA;cAAKkC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBnC,OAAA;gBAAKkC,SAAS,EAAC,0HAA0H;gBAAAC,QAAA,eACvInC,OAAA;kBAAKkC,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EAGL,CACC;gBAAE0B,IAAI,EAAE,OAAO;gBAAEC,KAAK,EAAE,aAAa;gBAAEC,QAAQ,EAAE;cAAgB,CAAC,EAClE;gBAAEF,IAAI,EAAE,SAAS;gBAAEC,KAAK,EAAE,cAAc;gBAAEC,QAAQ,EAAE;cAAkB,CAAC,EACvE;gBAAEF,IAAI,EAAE,YAAY;gBAAEC,KAAK,EAAE,eAAe;gBAAEC,QAAQ,EAAE;cAAoB,CAAC,EAC7E;gBAAEF,IAAI,EAAE,KAAK;gBAAEC,KAAK,EAAE,eAAe;gBAAEC,QAAQ,EAAE;cAAoB,CAAC,CACvE,CAAC1B,GAAG,CAAC,CAAC2B,IAAI,EAAEC,KAAK,kBAChB9E,OAAA,CAACV,MAAM,CAAC8C,GAAG;gBAETF,SAAS,EAAE,YAAY2C,IAAI,CAACD,QAAQ,IAAIC,IAAI,CAACF,KAAK,kEAAmE;gBACrHtC,OAAO,EAAE;kBACPoB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdlB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFC,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,MAAM,EAAEC,QAAQ;kBAChBgB,KAAK,EAAEmB,KAAK,GAAG;gBACjB,CAAE;gBAAA3C,QAAA,EAED0C,IAAI,CAACH;cAAI,GAZLG,IAAI,CAACH,IAAI;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaJ,CACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;QACTF,SAAS,EAAC,uDAAuD;QACjEG,OAAO,EAAE;UAAEoB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;QAAE,CAAE;QAC3BjB,UAAU,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAEC,MAAM,EAAEC;QAAS,CAAE;QAAAR,QAAA,eAE9CnC,OAAA;UAAKkC,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eACjFnC,OAAA;YAAKkC,SAAS,EAAC;UAAuC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC9C,EAAA,CArQID,IAAI;AAAA8E,EAAA,GAAJ9E,IAAI;AAuQV,eAAeA,IAAI;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}