{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\About.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGraduationCap, FaBriefcase, FaAward, FaHeart, FaCode, FaRocket, FaUsers, FaLightbulb } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('story');\n  const stats = [{\n    number: '5+',\n    label: 'Years Experience',\n    icon: FaRocket,\n    color: 'from-blue-500 to-cyan-500'\n  }, {\n    number: '50+',\n    label: 'Projects Completed',\n    icon: FaBriefcase,\n    color: 'from-green-500 to-emerald-500'\n  }, {\n    number: '15+',\n    label: 'Technologies',\n    icon: FaCode,\n    color: 'from-purple-500 to-pink-500'\n  }, {\n    number: '100%',\n    label: 'Client Satisfaction',\n    icon: FaHeart,\n    color: 'from-red-500 to-rose-500'\n  }];\n  const timeline = [{\n    year: '2023',\n    title: 'Senior Full Stack Developer',\n    company: 'Tech Innovations Inc.',\n    description: 'Leading development of enterprise-scale applications, mentoring junior developers, and architecting scalable solutions.',\n    icon: FaBriefcase,\n    type: 'work'\n  }, {\n    year: '2022',\n    title: 'AWS Certified Developer',\n    company: 'Amazon Web Services',\n    description: 'Achieved AWS certification demonstrating expertise in cloud architecture and serverless technologies.',\n    icon: FaAward,\n    type: 'achievement'\n  }, {\n    year: '2021',\n    title: 'Full Stack Developer',\n    company: 'Digital Solutions Ltd.',\n    description: 'Developed and maintained multiple client projects using React, Node.js, and cloud technologies.',\n    icon: FaBriefcase,\n    type: 'work'\n  }, {\n    year: '2020',\n    title: 'Computer Science Degree',\n    company: 'University of Technology',\n    description: 'Graduated with honors, specializing in software engineering and web technologies.',\n    icon: FaGraduationCap,\n    type: 'education'\n  }];\n  const values = [{\n    icon: FaLightbulb,\n    title: 'Innovation',\n    description: 'Always exploring new technologies and creative solutions to complex problems.'\n  }, {\n    icon: FaUsers,\n    title: 'Collaboration',\n    description: 'Believing in the power of teamwork and open communication to achieve great results.'\n  }, {\n    icon: FaRocket,\n    title: 'Excellence',\n    description: 'Committed to delivering high-quality code and exceptional user experiences.'\n  }, {\n    icon: FaHeart,\n    title: 'Passion',\n    description: 'Genuinely passionate about technology and its potential to make a positive impact.'\n  }];\n  const tabs = [{\n    id: 'story',\n    label: 'My Story',\n    icon: FaHeart\n  }, {\n    id: 'journey',\n    label: 'Journey',\n    icon: FaRocket\n  }, {\n    id: 'values',\n    label: 'Values',\n    icon: FaLightbulb\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"py-20 bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: \"About Me\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"Passionate developer with a love for creating innovative solutions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -50\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-3xl font-bold text-gray-900 mb-6\",\n            children: \"Full Stack Developer & Creative Problem Solver\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-6 leading-relaxed\",\n            children: \"I'm a passionate full-stack developer with over 3 years of experience creating digital solutions that make a difference. My journey in tech started with curiosity and has evolved into a career dedicated to building applications that are both beautiful and functional.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-8 leading-relaxed\",\n            children: \"I specialize in modern web technologies including React, Node.js, and cloud platforms. I believe in writing clean, maintainable code and creating user experiences that delight and inspire.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: skills.map((skill, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              whileInView: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-semibold text-gray-700\",\n                  children: skill.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [skill.level, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 128,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    width: 0\n                  },\n                  whileInView: {\n                    width: `${skill.level}%`\n                  },\n                  transition: {\n                    duration: 1,\n                    delay: index * 0.1\n                  },\n                  className: `${skill.color} h-2 rounded-full`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 133,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this)]\n            }, skill.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 50\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-6\",\n            children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.5\n              },\n              whileInView: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: index * 0.1\n              },\n              className: \"bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 text-center hover:shadow-lg transition-shadow duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: stat.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                children: stat.number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this)]\n            }, stat.label, true, {\n              fileName: _jsxFileName,\n              lineNumber: 154,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.4\n            },\n            className: \"mt-8 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-48 h-48 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center shadow-xl\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl\",\n                children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 175,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-900 mb-8\",\n          children: \"Technologies I Work With\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap justify-center gap-4\",\n          children: ['React', 'Node.js', 'JavaScript', 'TypeScript', 'Python', 'MongoDB', 'PostgreSQL', 'AWS', 'Docker', 'Git'].map((tech, index) => /*#__PURE__*/_jsxDEV(motion.span, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.3,\n              delay: index * 0.05\n            },\n            className: \"px-6 py-3 bg-gradient-to-r from-blue-50 to-purple-50 text-gray-700 rounded-full font-medium hover:shadow-md transition-shadow duration-300\",\n            children: tech\n          }, tech, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 190,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"text-center mt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold mb-4\",\n            children: \"Ready to work together?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg mb-6 opacity-90\",\n            children: \"Let's create something amazing that makes a difference\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#contact\",\n            className: \"inline-flex items-center px-8 py-3 bg-white text-blue-600 rounded-full font-semibold hover:shadow-lg transition-all duration-300\",\n            children: [\"Get In Touch\", /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 ml-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 209,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n};\n_s(About, \"xrVXYcbQxoKv7AZJ0h61LqKtyPU=\");\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "FaGraduationCap", "FaBriefcase", "FaAward", "FaHeart", "FaCode", "FaRocket", "FaUsers", "FaLightbulb", "jsxDEV", "_jsxDEV", "About", "_s", "activeTab", "setActiveTab", "stats", "number", "label", "icon", "color", "timeline", "year", "title", "company", "description", "type", "values", "tabs", "id", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "x", "skills", "map", "skill", "index", "delay", "name", "level", "width", "stat", "scale", "tech", "span", "href", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/About.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGraduationCap, FaBriefcase, FaAward, FaHeart, FaCode, FaRocket, FaUsers, FaLightbulb } from 'react-icons/fa';\n\nconst About = () => {\n  const [activeTab, setActiveTab] = useState('story');\n\n  const stats = [\n    { number: '5+', label: 'Years Experience', icon: FaRocket, color: 'from-blue-500 to-cyan-500' },\n    { number: '50+', label: 'Projects Completed', icon: FaBriefcase, color: 'from-green-500 to-emerald-500' },\n    { number: '15+', label: 'Technologies', icon: FaCode, color: 'from-purple-500 to-pink-500' },\n    { number: '100%', label: 'Client Satisfaction', icon: FaHeart, color: 'from-red-500 to-rose-500' }\n  ];\n\n  const timeline = [\n    {\n      year: '2023',\n      title: 'Senior Full Stack Developer',\n      company: 'Tech Innovations Inc.',\n      description: 'Leading development of enterprise-scale applications, mentoring junior developers, and architecting scalable solutions.',\n      icon: FaBriefcase,\n      type: 'work'\n    },\n    {\n      year: '2022',\n      title: 'AWS Certified Developer',\n      company: 'Amazon Web Services',\n      description: 'Achieved AWS certification demonstrating expertise in cloud architecture and serverless technologies.',\n      icon: FaAward,\n      type: 'achievement'\n    },\n    {\n      year: '2021',\n      title: 'Full Stack Developer',\n      company: 'Digital Solutions Ltd.',\n      description: 'Developed and maintained multiple client projects using React, Node.js, and cloud technologies.',\n      icon: FaBriefcase,\n      type: 'work'\n    },\n    {\n      year: '2020',\n      title: 'Computer Science Degree',\n      company: 'University of Technology',\n      description: 'Graduated with honors, specializing in software engineering and web technologies.',\n      icon: FaGraduationCap,\n      type: 'education'\n    }\n  ];\n\n  const values = [\n    {\n      icon: FaLightbulb,\n      title: 'Innovation',\n      description: 'Always exploring new technologies and creative solutions to complex problems.'\n    },\n    {\n      icon: FaUsers,\n      title: 'Collaboration',\n      description: 'Believing in the power of teamwork and open communication to achieve great results.'\n    },\n    {\n      icon: FaRocket,\n      title: 'Excellence',\n      description: 'Committed to delivering high-quality code and exceptional user experiences.'\n    },\n    {\n      icon: FaHeart,\n      title: 'Passion',\n      description: 'Genuinely passionate about technology and its potential to make a positive impact.'\n    }\n  ];\n\n  const tabs = [\n    { id: 'story', label: 'My Story', icon: FaHeart },\n    { id: 'journey', label: 'Journey', icon: FaRocket },\n    { id: 'values', label: 'Values', icon: FaLightbulb }\n  ];\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">About Me</h2>\n          <div className=\"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"></div>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            Passionate developer with a love for creating innovative solutions\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20\">\n          {/* Left side - Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">\n              Full Stack Developer & Creative Problem Solver\n            </h3>\n            \n            <p className=\"text-lg text-gray-600 mb-6 leading-relaxed\">\n              I'm a passionate full-stack developer with over 3 years of experience \n              creating digital solutions that make a difference. My journey in tech \n              started with curiosity and has evolved into a career dedicated to \n              building applications that are both beautiful and functional.\n            </p>\n            \n            <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n              I specialize in modern web technologies including React, Node.js, and \n              cloud platforms. I believe in writing clean, maintainable code and \n              creating user experiences that delight and inspire.\n            </p>\n\n            {/* Skills bars */}\n            <div className=\"space-y-6\">\n              {skills.map((skill, index) => (\n                <motion.div\n                  key={skill.name}\n                  initial={{ opacity: 0, x: -20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                >\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <span className=\"text-sm font-semibold text-gray-700\">{skill.name}</span>\n                    <span className=\"text-sm text-gray-500\">{skill.level}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <motion.div\n                      initial={{ width: 0 }}\n                      whileInView={{ width: `${skill.level}%` }}\n                      transition={{ duration: 1, delay: index * 0.1 }}\n                      className={`${skill.color} h-2 rounded-full`}\n                    ></motion.div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Right side - Stats */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"relative\"\n          >\n            <div className=\"grid grid-cols-2 gap-6\">\n              {stats.map((stat, index) => (\n                <motion.div\n                  key={stat.label}\n                  initial={{ opacity: 0, scale: 0.5 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  className=\"bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 text-center hover:shadow-lg transition-shadow duration-300\"\n                >\n                  <div className=\"text-4xl mb-4\">{stat.icon}</div>\n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">{stat.number}</div>\n                  <div className=\"text-sm text-gray-600 font-medium\">{stat.label}</div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Profile image placeholder */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              className=\"mt-8 flex justify-center\"\n            >\n              <div className=\"w-48 h-48 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center shadow-xl\">\n                <div className=\"text-6xl\">👨‍💻</div>\n              </div>\n            </motion.div>\n          </motion.div>\n        </div>\n\n        {/* Technologies */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">Technologies I Work With</h3>\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {[\n              'React', 'Node.js', 'JavaScript', 'TypeScript', 'Python', \n              'MongoDB', 'PostgreSQL', 'AWS', 'Docker', 'Git'\n            ].map((tech, index) => (\n              <motion.span\n                key={tech}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3, delay: index * 0.05 }}\n                className=\"px-6 py-3 bg-gradient-to-r from-blue-50 to-purple-50 text-gray-700 rounded-full font-medium hover:shadow-md transition-shadow duration-300\"\n              >\n                {tech}\n              </motion.span>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Call to action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\">\n            <h3 className=\"text-2xl font-bold mb-4\">Ready to work together?</h3>\n            <p className=\"text-lg mb-6 opacity-90\">\n              Let's create something amazing that makes a difference\n            </p>\n            <a\n              href=\"#contact\"\n              className=\"inline-flex items-center px-8 py-3 bg-white text-blue-600 rounded-full font-semibold hover:shadow-lg transition-all duration-300\"\n            >\n              Get In Touch\n              <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </a>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,eAAe,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExH,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,OAAO,CAAC;EAEnD,MAAMiB,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAEZ,QAAQ;IAAEa,KAAK,EAAE;EAA4B,CAAC,EAC/F;IAAEH,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,EAAEhB,WAAW;IAAEiB,KAAK,EAAE;EAAgC,CAAC,EACzG;IAAEH,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAEb,MAAM;IAAEc,KAAK,EAAE;EAA8B,CAAC,EAC5F;IAAEH,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAEd,OAAO;IAAEe,KAAK,EAAE;EAA2B,CAAC,CACnG;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,uBAAuB;IAChCC,WAAW,EAAE,yHAAyH;IACtIN,IAAI,EAAEhB,WAAW;IACjBuB,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,qBAAqB;IAC9BC,WAAW,EAAE,uGAAuG;IACpHN,IAAI,EAAEf,OAAO;IACbsB,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,sBAAsB;IAC7BC,OAAO,EAAE,wBAAwB;IACjCC,WAAW,EAAE,iGAAiG;IAC9GN,IAAI,EAAEhB,WAAW;IACjBuB,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,mFAAmF;IAChGN,IAAI,EAAEjB,eAAe;IACrBwB,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,MAAM,GAAG,CACb;IACER,IAAI,EAAEV,WAAW;IACjBc,KAAK,EAAE,YAAY;IACnBE,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAEX,OAAO;IACbe,KAAK,EAAE,eAAe;IACtBE,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAEZ,QAAQ;IACdgB,KAAK,EAAE,YAAY;IACnBE,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAEd,OAAO;IACbkB,KAAK,EAAE,SAAS;IAChBE,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMG,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,OAAO;IAAEX,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAEd;EAAQ,CAAC,EACjD;IAAEwB,EAAE,EAAE,SAAS;IAAEX,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEZ;EAAS,CAAC,EACnD;IAAEsB,EAAE,EAAE,QAAQ;IAAEX,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAEV;EAAY,CAAC,CACrD;EAED,oBACEE,OAAA;IAASkB,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC5CpB,OAAA;MAAKmB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDpB,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BpB,OAAA;UAAImB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAQ;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnE/B,OAAA;UAAKmB,SAAS,EAAC;QAAoE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1F/B,OAAA;UAAGmB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEb/B,OAAA;QAAKmB,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBAExEpB,OAAA,CAACX,MAAM,CAACgC,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCP,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,gBAE9BpB,OAAA;YAAImB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAEL/B,OAAA;YAAGmB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAK1D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJ/B,OAAA;YAAGmB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAI1D;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJ/B,OAAA;YAAKmB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBa,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBpC,OAAA,CAACX,MAAM,CAACgC,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCP,WAAW,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAES,CAAC,EAAE;cAAE,CAAE;cAClCN,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEU,KAAK,EAAED,KAAK,GAAG;cAAI,CAAE;cAAAhB,QAAA,gBAElDpB,OAAA;gBAAKmB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDpB,OAAA;kBAAMmB,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAEe,KAAK,CAACG;gBAAI;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzE/B,OAAA;kBAAMmB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAEe,KAAK,CAACI,KAAK,EAAC,GAAC;gBAAA;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACN/B,OAAA;gBAAKmB,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClDpB,OAAA,CAACX,MAAM,CAACgC,GAAG;kBACTC,OAAO,EAAE;oBAAEkB,KAAK,EAAE;kBAAE,CAAE;kBACtBf,WAAW,EAAE;oBAAEe,KAAK,EAAE,GAAGL,KAAK,CAACI,KAAK;kBAAI,CAAE;kBAC1Cb,UAAU,EAAE;oBAAEC,QAAQ,EAAE,CAAC;oBAAEU,KAAK,EAAED,KAAK,GAAG;kBAAI,CAAE;kBAChDjB,SAAS,EAAE,GAAGgB,KAAK,CAAC1B,KAAK;gBAAoB;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA,GAhBDI,KAAK,CAACG,IAAI;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBL,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb/B,OAAA,CAACX,MAAM,CAACgC,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAG,CAAE;UAC/BP,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAES,CAAC,EAAE;UAAE,CAAE;UAClCN,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BR,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAEpBpB,OAAA;YAAKmB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACpCf,KAAK,CAAC6B,GAAG,CAAC,CAACO,IAAI,EAAEL,KAAK,kBACrBpC,OAAA,CAACX,MAAM,CAACgC,GAAG;cAETC,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEmB,KAAK,EAAE;cAAI,CAAE;cACpCjB,WAAW,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEmB,KAAK,EAAE;cAAE,CAAE;cACtChB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEU,KAAK,EAAED,KAAK,GAAG;cAAI,CAAE;cAClDjB,SAAS,EAAC,wHAAwH;cAAAC,QAAA,gBAElIpB,OAAA;gBAAKmB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAEqB,IAAI,CAACjC;cAAI;gBAAAoB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChD/B,OAAA;gBAAKmB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAEqB,IAAI,CAACnC;cAAM;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1E/B,OAAA;gBAAKmB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEqB,IAAI,CAAClC;cAAK;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GARhEU,IAAI,CAAClC,KAAK;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASL,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN/B,OAAA,CAACX,MAAM,CAACgC,GAAG;YACTC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEmB,KAAK,EAAE;YAAI,CAAE;YACpCjB,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEmB,KAAK,EAAE;YAAE,CAAE;YACtChB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEU,KAAK,EAAE;YAAI,CAAE;YAC1ClB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eAEpCpB,OAAA;cAAKmB,SAAS,EAAC,iHAAiH;cAAAC,QAAA,eAC9HpB,OAAA;gBAAKmB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAK;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGN/B,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBpB,OAAA;UAAImB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAwB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnF/B,OAAA;UAAKmB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjD,CACC,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EACxD,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,CAChD,CAACc,GAAG,CAAC,CAACS,IAAI,EAAEP,KAAK,kBAChBpC,OAAA,CAACX,MAAM,CAACuD,IAAI;YAEVtB,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEmB,KAAK,EAAE;YAAI,CAAE;YACpCjB,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEmB,KAAK,EAAE;YAAE,CAAE;YACtChB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEU,KAAK,EAAED,KAAK,GAAG;YAAK,CAAE;YACnDjB,SAAS,EAAC,4IAA4I;YAAAC,QAAA,EAErJuB;UAAI,GANAA,IAAI;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOE,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb/B,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEU,KAAK,EAAE;QAAI,CAAE;QAC1ClB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAE7BpB,OAAA;UAAKmB,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFpB,OAAA;YAAImB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAuB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpE/B,OAAA;YAAGmB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJ/B,OAAA;YACE6C,IAAI,EAAC,UAAU;YACf1B,SAAS,EAAC,kIAAkI;YAAAC,QAAA,GAC7I,cAEC,eAAApB,OAAA;cAAKmB,SAAS,EAAC,cAAc;cAAC2B,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA5B,QAAA,eACjFpB,OAAA;gBAAMiD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA0B;gBAAAxB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC7B,EAAA,CArOID,KAAK;AAAAoD,EAAA,GAALpD,KAAK;AAuOX,eAAeA,KAAK;AAAC,IAAAoD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}