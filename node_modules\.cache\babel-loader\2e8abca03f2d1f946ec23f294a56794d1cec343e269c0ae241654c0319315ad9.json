{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\Projects.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGithub, FaExternalLinkAlt, FaCode, FaEye, FaStar, FaCalendarAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Projects = () => {\n  _s();\n  const [filter, setFilter] = useState('all');\n  const [hoveredProject, setHoveredProject] = useState(null);\n  const projects = [{\n    id: 1,\n    title: 'E-Commerce Platform',\n    description: 'A comprehensive full-stack e-commerce solution featuring modern UI/UX, secure payment processing, real-time inventory management, and advanced analytics dashboard.',\n    longDescription: 'Built with React and Node.js, this platform handles thousands of products and users. Features include JWT authentication, Stripe payments, email notifications, and a powerful admin panel with sales analytics.',\n    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',\n    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'JWT', 'Socket.io'],\n    category: 'fullstack',\n    github: '#',\n    demo: '#',\n    featured: true,\n    status: 'Live',\n    date: '2023',\n    stats: {\n      stars: 124,\n      forks: 45,\n      views: '2.1k'\n    }\n  }, {\n    id: 2,\n    title: 'AI-Powered Task Manager',\n    description: 'An intelligent task management application with AI-driven priority suggestions, real-time collaboration, and advanced analytics.',\n    longDescription: 'Features include drag-and-drop Kanban boards, AI task prioritization, team chat, file sharing, and detailed productivity analytics. Built with modern React patterns and Firebase real-time database.',\n    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop',\n    technologies: ['React', 'Firebase', 'OpenAI API', 'Material-UI', 'Chart.js'],\n    category: 'frontend',\n    github: '#',\n    demo: '#',\n    featured: true,\n    status: 'In Development',\n    date: '2023',\n    stats: {\n      stars: 89,\n      forks: 23,\n      views: '1.5k'\n    }\n  }, {\n    id: 3,\n    title: 'Real-Time Weather Analytics',\n    description: 'A comprehensive weather dashboard with predictive analytics, interactive maps, and personalized weather insights.',\n    longDescription: 'Integrates multiple weather APIs to provide accurate forecasts, severe weather alerts, and climate trend analysis. Features include location-based services, data visualization, and mobile-responsive design.',\n    image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=800&h=600&fit=crop',\n    technologies: ['React', 'OpenWeather API', 'D3.js', 'Mapbox', 'PWA'],\n    category: 'frontend',\n    github: '#',\n    demo: '#',\n    featured: false,\n    status: 'Live',\n    date: '2023',\n    stats: {\n      stars: 67,\n      forks: 18,\n      views: '980'\n    }\n  }, {\n    id: 4,\n    title: 'Microservices API Gateway',\n    description: 'A scalable REST API gateway with microservices architecture, advanced security, and comprehensive monitoring.',\n    longDescription: 'Enterprise-grade API server with JWT authentication, rate limiting, request/response logging, API documentation, and health monitoring. Supports multiple databases and caching strategies.',\n    image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?w=800&h=600&fit=crop',\n    technologies: ['Node.js', 'Express', 'Redis', 'Docker', 'Swagger', 'JWT'],\n    category: 'backend',\n    github: '#',\n    demo: '#',\n    featured: true,\n    status: 'Live',\n    date: '2022',\n    stats: {\n      stars: 156,\n      forks: 67,\n      views: '3.2k'\n    }\n  }, {\n    id: 5,\n    title: 'Social Media Analytics Hub',\n    description: 'An advanced social media analytics platform with AI-powered insights, automated reporting, and multi-platform integration.',\n    longDescription: 'Comprehensive dashboard that aggregates data from multiple social platforms, provides sentiment analysis, engagement metrics, and automated report generation. Features real-time data processing and predictive analytics.',\n    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',\n    technologies: ['React', 'D3.js', 'Node.js', 'PostgreSQL', 'Python', 'TensorFlow'],\n    category: 'fullstack',\n    github: '#',\n    demo: '#',\n    featured: false,\n    status: 'Live',\n    date: '2023',\n    stats: {\n      stars: 92,\n      forks: 34,\n      views: '1.8k'\n    }\n  }, {\n    id: 6,\n    title: 'Cloud-Native Mobile Backend',\n    description: 'Highly scalable serverless backend infrastructure with real-time messaging, push notifications, and global CDN.',\n    longDescription: 'Built on AWS Lambda and DynamoDB, this backend supports millions of users with real-time chat, file uploads, push notifications, and advanced caching. Includes comprehensive monitoring and auto-scaling.',\n    image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',\n    technologies: ['Node.js', 'AWS Lambda', 'DynamoDB', 'Socket.io', 'Redis', 'CloudFront'],\n    category: 'backend',\n    github: '#',\n    demo: '#',\n    featured: false,\n    status: 'Live',\n    date: '2022',\n    stats: {\n      stars: 78,\n      forks: 29,\n      views: '1.2k'\n    }\n  }];\n  const categories = [{\n    id: 'all',\n    name: 'All Projects',\n    count: projects.length\n  }, {\n    id: 'fullstack',\n    name: 'Full Stack',\n    count: projects.filter(p => p.category === 'fullstack').length\n  }, {\n    id: 'frontend',\n    name: 'Frontend',\n    count: projects.filter(p => p.category === 'frontend').length\n  }, {\n    id: 'backend',\n    name: 'Backend',\n    count: projects.filter(p => p.category === 'backend').length\n  }];\n  const filteredProjects = filter === 'all' ? projects : projects.filter(project => project.category === filter);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"projects\",\n    className: \"py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute top-1/3 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/5 to-purple-600/5 rounded-full blur-3xl\",\n        animate: {\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 25,\n          repeat: Infinity,\n          ease: \"linear\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.span, {\n          className: \"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-600 text-sm font-medium mb-4\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          whileInView: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            delay: 0.2\n          },\n          children: \"\\uD83D\\uDCBC My Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n          children: [\"Featured\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n            children: \" Projects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\",\n          initial: {\n            width: 0\n          },\n          whileInView: {\n            width: 80\n          },\n          transition: {\n            delay: 0.4,\n            duration: 0.8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"A curated selection of my best work showcasing technical expertise, creative problem-solving, and modern development practices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"flex flex-wrap justify-center gap-4 mb-12\",\n        children: categories.map((category, index) => /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: () => setFilter(category.id),\n          className: `relative px-6 py-3 rounded-full font-semibold transition-all duration-300 ${filter === category.id ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:shadow-lg border border-gray-200'}`,\n          whileHover: {\n            scale: 1.05,\n            y: -2\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          children: [category.name, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `ml-2 px-2 py-1 rounded-full text-xs ${filter === category.id ? 'bg-white/20 text-white' : 'bg-blue-100 text-blue-600'}`,\n            children: category.count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), filter === category.id && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full\",\n            layoutId: \"activeFilter\",\n            transition: {\n              type: \"spring\",\n              bounce: 0.2,\n              duration: 0.6\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this)]\n        }, category.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          className: \"group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden\",\n          children: [project.featured && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-4 left-4 z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold\",\n              children: \"Featured\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative h-48 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: project.image,\n              alt: project.title,\n              className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                  href: project.github,\n                  className: \"p-3 bg-white rounded-full text-gray-800 hover:bg-gray-100 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 242,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 241,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: project.demo,\n                  className: \"p-3 bg-blue-600 rounded-full text-white hover:bg-blue-700 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 250,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\",\n              children: project.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 259,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4 text-sm leading-relaxed\",\n              children: project.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 262,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-2\",\n              children: project.technologies.map(tech => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-3 py-1 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 rounded-full text-xs font-medium\",\n                children: tech\n              }, tech, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 15\n          }, this)]\n        }, project.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"text-center mt-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 mb-6\",\n          children: \"Interested in seeing more of my work?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 289,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://github.com\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-lg transition-all duration-300\",\n          children: [\"View All Projects on GitHub\", /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 ml-2\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s(Projects, \"+nsqUx+vE0MNXlvo+vNSlxJg/rY=\");\n_c = Projects;\nexport default Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaExternalLinkAlt", "FaCode", "FaEye", "FaStar", "FaCalendarAlt", "jsxDEV", "_jsxDEV", "Projects", "_s", "filter", "setFilter", "hoveredProject", "setHoveredProject", "projects", "id", "title", "description", "longDescription", "image", "technologies", "category", "github", "demo", "featured", "status", "date", "stats", "stars", "forks", "views", "categories", "name", "count", "length", "p", "filteredProjects", "project", "className", "children", "div", "animate", "scale", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "opacity", "y", "whileInView", "span", "delay", "width", "map", "index", "button", "onClick", "whileHover", "whileTap", "layoutId", "type", "bounce", "src", "alt", "href", "fill", "viewBox", "d", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "tech", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/Projects.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGithub, FaExternalLinkAlt, FaCode, FaEye, FaStar, FaCalendarAlt } from 'react-icons/fa';\n\nconst Projects = () => {\n  const [filter, setFilter] = useState('all');\n  const [hoveredProject, setHoveredProject] = useState(null);\n\n  const projects = [\n    {\n      id: 1,\n      title: 'E-Commerce Platform',\n      description: 'A comprehensive full-stack e-commerce solution featuring modern UI/UX, secure payment processing, real-time inventory management, and advanced analytics dashboard.',\n      longDescription: 'Built with React and Node.js, this platform handles thousands of products and users. Features include JWT authentication, Stripe payments, email notifications, and a powerful admin panel with sales analytics.',\n      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',\n      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'JWT', 'Socket.io'],\n      category: 'fullstack',\n      github: '#',\n      demo: '#',\n      featured: true,\n      status: 'Live',\n      date: '2023',\n      stats: { stars: 124, forks: 45, views: '2.1k' }\n    },\n    {\n      id: 2,\n      title: 'AI-Powered Task Manager',\n      description: 'An intelligent task management application with AI-driven priority suggestions, real-time collaboration, and advanced analytics.',\n      longDescription: 'Features include drag-and-drop Kanban boards, AI task prioritization, team chat, file sharing, and detailed productivity analytics. Built with modern React patterns and Firebase real-time database.',\n      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop',\n      technologies: ['React', 'Firebase', 'OpenAI API', 'Material-UI', 'Chart.js'],\n      category: 'frontend',\n      github: '#',\n      demo: '#',\n      featured: true,\n      status: 'In Development',\n      date: '2023',\n      stats: { stars: 89, forks: 23, views: '1.5k' }\n    },\n    {\n      id: 3,\n      title: 'Real-Time Weather Analytics',\n      description: 'A comprehensive weather dashboard with predictive analytics, interactive maps, and personalized weather insights.',\n      longDescription: 'Integrates multiple weather APIs to provide accurate forecasts, severe weather alerts, and climate trend analysis. Features include location-based services, data visualization, and mobile-responsive design.',\n      image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=800&h=600&fit=crop',\n      technologies: ['React', 'OpenWeather API', 'D3.js', 'Mapbox', 'PWA'],\n      category: 'frontend',\n      github: '#',\n      demo: '#',\n      featured: false,\n      status: 'Live',\n      date: '2023',\n      stats: { stars: 67, forks: 18, views: '980' }\n    },\n    {\n      id: 4,\n      title: 'Microservices API Gateway',\n      description: 'A scalable REST API gateway with microservices architecture, advanced security, and comprehensive monitoring.',\n      longDescription: 'Enterprise-grade API server with JWT authentication, rate limiting, request/response logging, API documentation, and health monitoring. Supports multiple databases and caching strategies.',\n      image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?w=800&h=600&fit=crop',\n      technologies: ['Node.js', 'Express', 'Redis', 'Docker', 'Swagger', 'JWT'],\n      category: 'backend',\n      github: '#',\n      demo: '#',\n      featured: true,\n      status: 'Live',\n      date: '2022',\n      stats: { stars: 156, forks: 67, views: '3.2k' }\n    },\n    {\n      id: 5,\n      title: 'Social Media Analytics Hub',\n      description: 'An advanced social media analytics platform with AI-powered insights, automated reporting, and multi-platform integration.',\n      longDescription: 'Comprehensive dashboard that aggregates data from multiple social platforms, provides sentiment analysis, engagement metrics, and automated report generation. Features real-time data processing and predictive analytics.',\n      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',\n      technologies: ['React', 'D3.js', 'Node.js', 'PostgreSQL', 'Python', 'TensorFlow'],\n      category: 'fullstack',\n      github: '#',\n      demo: '#',\n      featured: false,\n      status: 'Live',\n      date: '2023',\n      stats: { stars: 92, forks: 34, views: '1.8k' }\n    },\n    {\n      id: 6,\n      title: 'Cloud-Native Mobile Backend',\n      description: 'Highly scalable serverless backend infrastructure with real-time messaging, push notifications, and global CDN.',\n      longDescription: 'Built on AWS Lambda and DynamoDB, this backend supports millions of users with real-time chat, file uploads, push notifications, and advanced caching. Includes comprehensive monitoring and auto-scaling.',\n      image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',\n      technologies: ['Node.js', 'AWS Lambda', 'DynamoDB', 'Socket.io', 'Redis', 'CloudFront'],\n      category: 'backend',\n      github: '#',\n      demo: '#',\n      featured: false,\n      status: 'Live',\n      date: '2022',\n      stats: { stars: 78, forks: 29, views: '1.2k' }\n    }\n  ];\n\n  const categories = [\n    { id: 'all', name: 'All Projects', count: projects.length },\n    { id: 'fullstack', name: 'Full Stack', count: projects.filter(p => p.category === 'fullstack').length },\n    { id: 'frontend', name: 'Frontend', count: projects.filter(p => p.category === 'frontend').length },\n    { id: 'backend', name: 'Backend', count: projects.filter(p => p.category === 'backend').length }\n  ];\n\n  const filteredProjects = filter === 'all' \n    ? projects \n    : projects.filter(project => project.category === filter);\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden\">\n      {/* Background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute top-1/3 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/5 to-purple-600/5 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 25,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            className=\"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-600 text-sm font-medium mb-4\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            💼 My Portfolio\n          </motion.span>\n\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Featured\n            <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\"> Projects</span>\n          </h2>\n\n          <motion.div\n            className=\"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"\n            initial={{ width: 0 }}\n            whileInView={{ width: 80 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n          />\n\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            A curated selection of my best work showcasing technical expertise, creative problem-solving, and modern development practices\n          </p>\n        </motion.div>\n\n        {/* Filter Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\n        >\n          {categories.map((category, index) => (\n            <motion.button\n              key={category.id}\n              onClick={() => setFilter(category.id)}\n              className={`relative px-6 py-3 rounded-full font-semibold transition-all duration-300 ${\n                filter === category.id\n                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'\n                  : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:shadow-lg border border-gray-200'\n              }`}\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              {category.name}\n              <span className={`ml-2 px-2 py-1 rounded-full text-xs ${\n                filter === category.id\n                  ? 'bg-white/20 text-white'\n                  : 'bg-blue-100 text-blue-600'\n              }`}>\n                {category.count}\n              </span>\n\n              {filter === category.id && (\n                <motion.div\n                  className=\"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full\"\n                  layoutId=\"activeFilter\"\n                  transition={{ type: \"spring\", bounce: 0.2, duration: 0.6 }}\n                />\n              )}\n            </motion.button>\n          ))}\n        </motion.div>\n\n        {/* Projects Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {filteredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              className=\"group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden\"\n            >\n              {/* Featured badge */}\n              {project.featured && (\n                <div className=\"absolute top-4 left-4 z-10\">\n                  <span className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold\">\n                    Featured\n                  </span>\n                </div>\n              )}\n\n              {/* Project Image */}\n              <div className=\"relative h-48 overflow-hidden\">\n                <img\n                  src={project.image}\n                  alt={project.title}\n                  className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                \n                {/* Overlay buttons */}\n                <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <div className=\"flex space-x-4\">\n                    <a\n                      href={project.github}\n                      className=\"p-3 bg-white rounded-full text-gray-800 hover:bg-gray-100 transition-colors\"\n                    >\n                      <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n                      </svg>\n                    </a>\n                    <a\n                      href={project.demo}\n                      className=\"p-3 bg-blue-600 rounded-full text-white hover:bg-blue-700 transition-colors\"\n                    >\n                      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                      </svg>\n                    </a>\n                  </div>\n                </div>\n              </div>\n\n              {/* Project Content */}\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\">\n                  {project.title}\n                </h3>\n                <p className=\"text-gray-600 mb-4 text-sm leading-relaxed\">\n                  {project.description}\n                </p>\n                \n                {/* Technologies */}\n                <div className=\"flex flex-wrap gap-2\">\n                  {project.technologies.map((tech) => (\n                    <span\n                      key={tech}\n                      className=\"px-3 py-1 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 rounded-full text-xs font-medium\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <p className=\"text-lg text-gray-600 mb-6\">\n            Interested in seeing more of my work?\n          </p>\n          <a\n            href=\"https://github.com\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-lg transition-all duration-300\"\n          >\n            View All Projects on GitHub\n            <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnG,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMiB,QAAQ,GAAG,CACf;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,qKAAqK;IAClLC,eAAe,EAAE,kNAAkN;IACnOC,KAAK,EAAE,gFAAgF;IACvFC,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC;IAC3EC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAChD,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,kIAAkI;IAC/IC,eAAe,EAAE,uMAAuM;IACxNC,KAAK,EAAE,mFAAmF;IAC1FC,YAAY,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC;IAC5EC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,gBAAgB;IACxBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAC/C,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,mHAAmH;IAChIC,eAAe,EAAE,gNAAgN;IACjOC,KAAK,EAAE,mFAAmF;IAC1FC,YAAY,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;IACpEC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAM;EAC9C,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,+GAA+G;IAC5HC,eAAe,EAAE,6LAA6L;IAC9MC,KAAK,EAAE,gFAAgF;IACvFC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC;IACzEC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAChD,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,4BAA4B;IACnCC,WAAW,EAAE,4HAA4H;IACzIC,eAAe,EAAE,6NAA6N;IAC9OC,KAAK,EAAE,mFAAmF;IAC1FC,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC;IACjFC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAC/C,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,iHAAiH;IAC9HC,eAAe,EAAE,4MAA4M;IAC7NC,KAAK,EAAE,gFAAgF;IACvFC,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;IACvFC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAC/C,CAAC,CACF;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEhB,EAAE,EAAE,KAAK;IAAEiB,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAEnB,QAAQ,CAACoB;EAAO,CAAC,EAC3D;IAAEnB,EAAE,EAAE,WAAW;IAAEiB,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAEnB,QAAQ,CAACJ,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAACd,QAAQ,KAAK,WAAW,CAAC,CAACa;EAAO,CAAC,EACvG;IAAEnB,EAAE,EAAE,UAAU;IAAEiB,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAEnB,QAAQ,CAACJ,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAACd,QAAQ,KAAK,UAAU,CAAC,CAACa;EAAO,CAAC,EACnG;IAAEnB,EAAE,EAAE,SAAS;IAAEiB,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAEnB,QAAQ,CAACJ,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAACd,QAAQ,KAAK,SAAS,CAAC,CAACa;EAAO,CAAC,CACjG;EAED,MAAME,gBAAgB,GAAG1B,MAAM,KAAK,KAAK,GACrCI,QAAQ,GACRA,QAAQ,CAACJ,MAAM,CAAC2B,OAAO,IAAIA,OAAO,CAAChB,QAAQ,KAAKX,MAAM,CAAC;EAE3D,oBACEH,OAAA;IAASQ,EAAE,EAAC,UAAU;IAACuB,SAAS,EAAC,oFAAoF;IAAAC,QAAA,gBAEnHhC,OAAA;MAAK+B,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/ChC,OAAA,CAACT,MAAM,CAAC0C,GAAG;QACTF,SAAS,EAAC,4GAA4G;QACtHG,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN7C,OAAA;MAAK+B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEhC,OAAA,CAACT,MAAM,CAAC0C,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BP,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BhC,OAAA,CAACT,MAAM,CAAC2D,IAAI;UACVnB,SAAS,EAAC,yJAAyJ;UACnKe,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE;UAAI,CAAE;UACpCc,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE;UAAE,CAAE;UACtCE,UAAU,EAAE;YAAEc,KAAK,EAAE;UAAI,CAAE;UAAAnB,QAAA,EAC5B;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAEd7C,OAAA;UAAI+B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,GAAC,UAEhE,eAAAhC,OAAA;YAAM+B,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eAEL7C,OAAA,CAACT,MAAM,CAAC0C,GAAG;UACTF,SAAS,EAAC,oEAAoE;UAC9Ee,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBH,WAAW,EAAE;YAAEG,KAAK,EAAE;UAAG,CAAE;UAC3Bf,UAAU,EAAE;YAAEc,KAAK,EAAE,GAAG;YAAEb,QAAQ,EAAE;UAAI;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAEF7C,OAAA;UAAG+B,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb7C,OAAA,CAACT,MAAM,CAAC0C,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEa,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAEpDR,UAAU,CAAC6B,GAAG,CAAC,CAACvC,QAAQ,EAAEwC,KAAK,kBAC9BtD,OAAA,CAACT,MAAM,CAACgE,MAAM;UAEZC,OAAO,EAAEA,CAAA,KAAMpD,SAAS,CAACU,QAAQ,CAACN,EAAE,CAAE;UACtCuB,SAAS,EAAE,6EACT5B,MAAM,KAAKW,QAAQ,CAACN,EAAE,GAClB,mEAAmE,GACnE,0FAA0F,EAC7F;UACHiD,UAAU,EAAE;YAAEtB,KAAK,EAAE,IAAI;YAAEa,CAAC,EAAE,CAAC;UAAE,CAAE;UACnCU,QAAQ,EAAE;YAAEvB,KAAK,EAAE;UAAK,CAAE;UAC1BW,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/Bd,OAAO,EAAE;YAAEa,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BX,UAAU,EAAE;YAAEc,KAAK,EAAEG,KAAK,GAAG;UAAI,CAAE;UAAAtB,QAAA,GAElClB,QAAQ,CAACW,IAAI,eACdzB,OAAA;YAAM+B,SAAS,EAAE,uCACf5B,MAAM,KAAKW,QAAQ,CAACN,EAAE,GAClB,wBAAwB,GACxB,2BAA2B,EAC9B;YAAAwB,QAAA,EACAlB,QAAQ,CAACY;UAAK;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EAEN1C,MAAM,KAAKW,QAAQ,CAACN,EAAE,iBACrBR,OAAA,CAACT,MAAM,CAAC0C,GAAG;YACTF,SAAS,EAAC,kFAAkF;YAC5F4B,QAAQ,EAAC,cAAc;YACvBtB,UAAU,EAAE;cAAEuB,IAAI,EAAE,QAAQ;cAAEC,MAAM,EAAE,GAAG;cAAEvB,QAAQ,EAAE;YAAI;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CACF;QAAA,GA5BI/B,QAAQ,CAACN,EAAE;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BH,CAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGb7C,OAAA;QAAK+B,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEH,gBAAgB,CAACwB,GAAG,CAAC,CAACvB,OAAO,EAAEwB,KAAK,kBACnCtD,OAAA,CAACT,MAAM,CAAC0C,GAAG;UAETa,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCX,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEa,KAAK,EAAEG,KAAK,GAAG;UAAI,CAAE;UAClDvB,SAAS,EAAC,4GAA4G;UAAAC,QAAA,GAGrHF,OAAO,CAACb,QAAQ,iBACfjB,OAAA;YAAK+B,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzChC,OAAA;cAAM+B,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAAC;YAErH;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,eAGD7C,OAAA;YAAK+B,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC5ChC,OAAA;cACE8D,GAAG,EAAEhC,OAAO,CAAClB,KAAM;cACnBmD,GAAG,EAAEjC,OAAO,CAACrB,KAAM;cACnBsB,SAAS,EAAC;YAAoF;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eACF7C,OAAA;cAAK+B,SAAS,EAAC;YAAkI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGxJ7C,OAAA;cAAK+B,SAAS,EAAC,qHAAqH;cAAAC,QAAA,eAClIhC,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BhC,OAAA;kBACEgE,IAAI,EAAElC,OAAO,CAACf,MAAO;kBACrBgB,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,eAEvFhC,OAAA;oBAAK+B,SAAS,EAAC,SAAS;oBAACkC,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAlC,QAAA,eAC9DhC,OAAA;sBAAMmE,CAAC,EAAC;oBAA2sB;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACltB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACJ7C,OAAA;kBACEgE,IAAI,EAAElC,OAAO,CAACd,IAAK;kBACnBe,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,eAEvFhC,OAAA;oBAAK+B,SAAS,EAAC,SAAS;oBAACkC,IAAI,EAAC,MAAM;oBAACG,MAAM,EAAC,cAAc;oBAACF,OAAO,EAAC,WAAW;oBAAAlC,QAAA,eAC5EhC,OAAA;sBAAMqE,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACJ,CAAC,EAAC;oBAA8E;sBAAAzB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN7C,OAAA;YAAK+B,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBhC,OAAA;cAAI+B,SAAS,EAAC,kFAAkF;cAAAC,QAAA,EAC7FF,OAAO,CAACrB;YAAK;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACL7C,OAAA;cAAG+B,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACtDF,OAAO,CAACpB;YAAW;cAAAgC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eAGJ7C,OAAA;cAAK+B,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCF,OAAO,CAACjB,YAAY,CAACwC,GAAG,CAAEmB,IAAI,iBAC7BxE,OAAA;gBAEE+B,SAAS,EAAC,qGAAqG;gBAAAC,QAAA,EAE9GwC;cAAI,GAHAA,IAAI;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIL,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAnEDf,OAAO,CAACtB,EAAE;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoEL,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN7C,OAAA,CAACT,MAAM,CAAC0C,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEa,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BhC,OAAA;UAAG+B,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJ7C,OAAA;UACEgE,IAAI,EAAC,oBAAoB;UACzBS,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzB3C,SAAS,EAAC,mKAAmK;UAAAC,QAAA,GAC9K,6BAEC,eAAAhC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAACkC,IAAI,EAAC,MAAM;YAACG,MAAM,EAAC,cAAc;YAACF,OAAO,EAAC,WAAW;YAAAlC,QAAA,eACjFhC,OAAA;cAAMqE,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACJ,CAAC,EAAC;YAA0B;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC3C,EAAA,CA9SID,QAAQ;AAAA0E,EAAA,GAAR1E,QAAQ;AAgTd,eAAeA,QAAQ;AAAC,IAAA0E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}