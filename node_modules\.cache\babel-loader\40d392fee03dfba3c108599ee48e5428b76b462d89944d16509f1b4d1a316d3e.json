{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\Projects.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGithub, FaExternalLinkAlt, FaCode, FaEye, FaStar, FaCalendarAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Projects = () => {\n  _s();\n  const [filter, setFilter] = useState('all');\n  const projects = [{\n    id: 1,\n    title: 'E-Commerce Platform',\n    description: 'A full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment processing, and admin dashboard.',\n    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=500&h=300&fit=crop',\n    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],\n    category: 'fullstack',\n    github: '#',\n    demo: '#',\n    featured: true\n  }, {\n    id: 2,\n    title: 'Task Management App',\n    description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',\n    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop',\n    technologies: ['React', 'Firebase', 'Material-UI'],\n    category: 'frontend',\n    github: '#',\n    demo: '#',\n    featured: false\n  }, {\n    id: 3,\n    title: 'Weather Dashboard',\n    description: 'A responsive weather application with location-based forecasts, interactive maps, and detailed weather analytics.',\n    image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=500&h=300&fit=crop',\n    technologies: ['React', 'OpenWeather API', 'Chart.js'],\n    category: 'frontend',\n    github: '#',\n    demo: '#',\n    featured: false\n  }, {\n    id: 4,\n    title: 'REST API Server',\n    description: 'A robust REST API server with authentication, rate limiting, and comprehensive documentation. Built with Node.js and Express.',\n    image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&h=300&fit=crop',\n    technologies: ['Node.js', 'Express', 'JWT', 'Swagger'],\n    category: 'backend',\n    github: '#',\n    demo: '#',\n    featured: true\n  }, {\n    id: 5,\n    title: 'Social Media Dashboard',\n    description: 'A comprehensive social media analytics dashboard with data visualization and automated reporting features.',\n    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop',\n    technologies: ['React', 'D3.js', 'Node.js', 'PostgreSQL'],\n    category: 'fullstack',\n    github: '#',\n    demo: '#',\n    featured: false\n  }, {\n    id: 6,\n    title: 'Mobile App Backend',\n    description: 'Scalable backend infrastructure for a mobile application with real-time messaging and push notifications.',\n    image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=500&h=300&fit=crop',\n    technologies: ['Node.js', 'Socket.io', 'Redis', 'AWS'],\n    category: 'backend',\n    github: '#',\n    demo: '#',\n    featured: false\n  }];\n  const categories = [{\n    id: 'all',\n    name: 'All Projects',\n    count: projects.length\n  }, {\n    id: 'fullstack',\n    name: 'Full Stack',\n    count: projects.filter(p => p.category === 'fullstack').length\n  }, {\n    id: 'frontend',\n    name: 'Frontend',\n    count: projects.filter(p => p.category === 'frontend').length\n  }, {\n    id: 'backend',\n    name: 'Backend',\n    count: projects.filter(p => p.category === 'backend').length\n  }];\n  const filteredProjects = filter === 'all' ? projects : projects.filter(project => project.category === filter);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"projects\",\n    className: \"py-20 bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: \"Featured Projects\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"A showcase of my recent work and technical expertise\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"flex flex-wrap justify-center gap-4 mb-12\",\n        children: categories.map(category => /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setFilter(category.id),\n          className: `px-6 py-3 rounded-full font-semibold transition-all duration-300 ${filter === category.id ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`,\n          children: [category.name, \" (\", category.count, \")\"]\n        }, category.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n        children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          className: \"group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden\",\n          children: [project.featured && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-4 left-4 z-10\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold\",\n              children: \"Featured\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative h-48 overflow-hidden\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: project.image,\n              alt: project.title,\n              className: \"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex space-x-4\",\n                children: [/*#__PURE__*/_jsxDEV(\"a\", {\n                  href: project.github,\n                  className: \"p-3 bg-white rounded-full text-gray-800 hover:bg-gray-100 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      d: \"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 162,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 161,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: project.demo,\n                  className: \"p-3 bg-blue-600 rounded-full text-white hover:bg-blue-700 transition-colors\",\n                  children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-5 h-5\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      strokeLinecap: \"round\",\n                      strokeLinejoin: \"round\",\n                      strokeWidth: 2,\n                      d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 170,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\",\n              children: project.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 mb-4 text-sm leading-relaxed\",\n              children: project.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-wrap gap-2\",\n              children: project.technologies.map(tech => /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-3 py-1 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 rounded-full text-xs font-medium\",\n                children: tech\n              }, tech, false, {\n                fileName: _jsxFileName,\n                lineNumber: 189,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this)]\n        }, project.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"text-center mt-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-lg text-gray-600 mb-6\",\n          children: \"Interested in seeing more of my work?\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"https://github.com\",\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-lg transition-all duration-300\",\n          children: [\"View All Projects on GitHub\", /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-5 h-5 ml-2\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 212,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 203,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 89,\n    columnNumber: 5\n  }, this);\n};\n_s(Projects, \"W/UjwQokI5qYZ80z+yO8skpbfNg=\");\n_c = Projects;\nexport default Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaExternalLinkAlt", "FaCode", "FaEye", "FaStar", "FaCalendarAlt", "jsxDEV", "_jsxDEV", "Projects", "_s", "filter", "setFilter", "projects", "id", "title", "description", "image", "technologies", "category", "github", "demo", "featured", "categories", "name", "count", "length", "p", "filteredProjects", "project", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "map", "onClick", "index", "src", "alt", "href", "fill", "viewBox", "d", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "tech", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/Projects.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGithub, FaExternalLinkAlt, FaCode, FaEye, FaStar, FaCalendarAlt } from 'react-icons/fa';\n\nconst Projects = () => {\n  const [filter, setFilter] = useState('all');\n\n  const projects = [\n    {\n      id: 1,\n      title: 'E-Commerce Platform',\n      description: 'A full-stack e-commerce solution with React, Node.js, and MongoDB. Features include user authentication, payment processing, and admin dashboard.',\n      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=500&h=300&fit=crop',\n      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe'],\n      category: 'fullstack',\n      github: '#',\n      demo: '#',\n      featured: true\n    },\n    {\n      id: 2,\n      title: 'Task Management App',\n      description: 'A collaborative task management application with real-time updates, drag-and-drop functionality, and team collaboration features.',\n      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=500&h=300&fit=crop',\n      technologies: ['React', 'Firebase', 'Material-UI'],\n      category: 'frontend',\n      github: '#',\n      demo: '#',\n      featured: false\n    },\n    {\n      id: 3,\n      title: 'Weather Dashboard',\n      description: 'A responsive weather application with location-based forecasts, interactive maps, and detailed weather analytics.',\n      image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=500&h=300&fit=crop',\n      technologies: ['React', 'OpenWeather API', 'Chart.js'],\n      category: 'frontend',\n      github: '#',\n      demo: '#',\n      featured: false\n    },\n    {\n      id: 4,\n      title: 'REST API Server',\n      description: 'A robust REST API server with authentication, rate limiting, and comprehensive documentation. Built with Node.js and Express.',\n      image: 'https://images.unsplash.com/photo-1558494949-ef010cbdcc31?w=500&h=300&fit=crop',\n      technologies: ['Node.js', 'Express', 'JWT', 'Swagger'],\n      category: 'backend',\n      github: '#',\n      demo: '#',\n      featured: true\n    },\n    {\n      id: 5,\n      title: 'Social Media Dashboard',\n      description: 'A comprehensive social media analytics dashboard with data visualization and automated reporting features.',\n      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=500&h=300&fit=crop',\n      technologies: ['React', 'D3.js', 'Node.js', 'PostgreSQL'],\n      category: 'fullstack',\n      github: '#',\n      demo: '#',\n      featured: false\n    },\n    {\n      id: 6,\n      title: 'Mobile App Backend',\n      description: 'Scalable backend infrastructure for a mobile application with real-time messaging and push notifications.',\n      image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=500&h=300&fit=crop',\n      technologies: ['Node.js', 'Socket.io', 'Redis', 'AWS'],\n      category: 'backend',\n      github: '#',\n      demo: '#',\n      featured: false\n    }\n  ];\n\n  const categories = [\n    { id: 'all', name: 'All Projects', count: projects.length },\n    { id: 'fullstack', name: 'Full Stack', count: projects.filter(p => p.category === 'fullstack').length },\n    { id: 'frontend', name: 'Frontend', count: projects.filter(p => p.category === 'frontend').length },\n    { id: 'backend', name: 'Backend', count: projects.filter(p => p.category === 'backend').length }\n  ];\n\n  const filteredProjects = filter === 'all' \n    ? projects \n    : projects.filter(project => project.category === filter);\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">Featured Projects</h2>\n          <div className=\"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"></div>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            A showcase of my recent work and technical expertise\n          </p>\n        </motion.div>\n\n        {/* Filter Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\n        >\n          {categories.map((category) => (\n            <button\n              key={category.id}\n              onClick={() => setFilter(category.id)}\n              className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${\n                filter === category.id\n                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n              }`}\n            >\n              {category.name} ({category.count})\n            </button>\n          ))}\n        </motion.div>\n\n        {/* Projects Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n          {filteredProjects.map((project, index) => (\n            <motion.div\n              key={project.id}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              className=\"group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden\"\n            >\n              {/* Featured badge */}\n              {project.featured && (\n                <div className=\"absolute top-4 left-4 z-10\">\n                  <span className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold\">\n                    Featured\n                  </span>\n                </div>\n              )}\n\n              {/* Project Image */}\n              <div className=\"relative h-48 overflow-hidden\">\n                <img\n                  src={project.image}\n                  alt={project.title}\n                  className=\"w-full h-full object-cover group-hover:scale-110 transition-transform duration-500\"\n                />\n                <div className=\"absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"></div>\n                \n                {/* Overlay buttons */}\n                <div className=\"absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300\">\n                  <div className=\"flex space-x-4\">\n                    <a\n                      href={project.github}\n                      className=\"p-3 bg-white rounded-full text-gray-800 hover:bg-gray-100 transition-colors\"\n                    >\n                      <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path d=\"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z\"/>\n                      </svg>\n                    </a>\n                    <a\n                      href={project.demo}\n                      className=\"p-3 bg-blue-600 rounded-full text-white hover:bg-blue-700 transition-colors\"\n                    >\n                      <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                      </svg>\n                    </a>\n                  </div>\n                </div>\n              </div>\n\n              {/* Project Content */}\n              <div className=\"p-6\">\n                <h3 className=\"text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors\">\n                  {project.title}\n                </h3>\n                <p className=\"text-gray-600 mb-4 text-sm leading-relaxed\">\n                  {project.description}\n                </p>\n                \n                {/* Technologies */}\n                <div className=\"flex flex-wrap gap-2\">\n                  {project.technologies.map((tech) => (\n                    <span\n                      key={tech}\n                      className=\"px-3 py-1 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 rounded-full text-xs font-medium\"\n                    >\n                      {tech}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <p className=\"text-lg text-gray-600 mb-6\">\n            Interested in seeing more of my work?\n          </p>\n          <a\n            href=\"https://github.com\"\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-lg transition-all duration-300\"\n          >\n            View All Projects on GitHub\n            <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n            </svg>\n          </a>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnG,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAE3C,MAAMe,QAAQ,GAAG,CACf;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,mJAAmJ;IAChKC,KAAK,EAAE,gFAAgF;IACvFC,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,CAAC;IACvDC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,mIAAmI;IAChJC,KAAK,EAAE,mFAAmF;IAC1FC,YAAY,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC;IAClDC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,mHAAmH;IAChIC,KAAK,EAAE,mFAAmF;IAC1FC,YAAY,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,UAAU,CAAC;IACtDC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,iBAAiB;IACxBC,WAAW,EAAE,+HAA+H;IAC5IC,KAAK,EAAE,gFAAgF;IACvFC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,CAAC;IACtDC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,4GAA4G;IACzHC,KAAK,EAAE,mFAAmF;IAC1FC,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC;IACzDC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE;EACZ,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,2GAA2G;IACxHC,KAAK,EAAE,gFAAgF;IACvFC,YAAY,EAAE,CAAC,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,CAAC;IACtDC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,UAAU,GAAG,CACjB;IAAET,EAAE,EAAE,KAAK;IAAEU,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAEZ,QAAQ,CAACa;EAAO,CAAC,EAC3D;IAAEZ,EAAE,EAAE,WAAW;IAAEU,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAEZ,QAAQ,CAACF,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAK,WAAW,CAAC,CAACO;EAAO,CAAC,EACvG;IAAEZ,EAAE,EAAE,UAAU;IAAEU,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAEZ,QAAQ,CAACF,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAK,UAAU,CAAC,CAACO;EAAO,CAAC,EACnG;IAAEZ,EAAE,EAAE,SAAS;IAAEU,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAEZ,QAAQ,CAACF,MAAM,CAACgB,CAAC,IAAIA,CAAC,CAACR,QAAQ,KAAK,SAAS,CAAC,CAACO;EAAO,CAAC,CACjG;EAED,MAAME,gBAAgB,GAAGjB,MAAM,KAAK,KAAK,GACrCE,QAAQ,GACRA,QAAQ,CAACF,MAAM,CAACkB,OAAO,IAAIA,OAAO,CAACV,QAAQ,KAAKR,MAAM,CAAC;EAE3D,oBACEH,OAAA;IAASM,EAAE,EAAC,UAAU;IAACgB,SAAS,EAAC,gBAAgB;IAAAC,QAAA,eAC/CvB,OAAA;MAAKsB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDvB,OAAA,CAACT,MAAM,CAACiC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BvB,OAAA;UAAIsB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAiB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5ElC,OAAA;UAAKsB,SAAS,EAAC;QAAoE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1FlC,OAAA;UAAGsB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGblC,OAAA,CAACT,MAAM,CAACiC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAEpDR,UAAU,CAACqB,GAAG,CAAEzB,QAAQ,iBACvBX,OAAA;UAEEqC,OAAO,EAAEA,CAAA,KAAMjC,SAAS,CAACO,QAAQ,CAACL,EAAE,CAAE;UACtCgB,SAAS,EAAE,oEACTnB,MAAM,KAAKQ,QAAQ,CAACL,EAAE,GAClB,mEAAmE,GACnE,6CAA6C,EAChD;UAAAiB,QAAA,GAEFZ,QAAQ,CAACK,IAAI,EAAC,IAAE,EAACL,QAAQ,CAACM,KAAK,EAAC,GACnC;QAAA,GATON,QAAQ,CAACL,EAAE;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACT;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGblC,OAAA;QAAKsB,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClEH,gBAAgB,CAACgB,GAAG,CAAC,CAACf,OAAO,EAAEiB,KAAK,kBACnCtC,OAAA,CAACT,MAAM,CAACiC,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEK,KAAK,EAAEG,KAAK,GAAG;UAAI,CAAE;UAClDhB,SAAS,EAAC,4GAA4G;UAAAC,QAAA,GAGrHF,OAAO,CAACP,QAAQ,iBACfd,OAAA;YAAKsB,SAAS,EAAC,4BAA4B;YAAAC,QAAA,eACzCvB,OAAA;cAAMsB,SAAS,EAAC,oGAAoG;cAAAC,QAAA,EAAC;YAErH;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CACN,eAGDlC,OAAA;YAAKsB,SAAS,EAAC,+BAA+B;YAAAC,QAAA,gBAC5CvB,OAAA;cACEuC,GAAG,EAAElB,OAAO,CAACZ,KAAM;cACnB+B,GAAG,EAAEnB,OAAO,CAACd,KAAM;cACnBe,SAAS,EAAC;YAAoF;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC,eACFlC,OAAA;cAAKsB,SAAS,EAAC;YAAkI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAGxJlC,OAAA;cAAKsB,SAAS,EAAC,qHAAqH;cAAAC,QAAA,eAClIvB,OAAA;gBAAKsB,SAAS,EAAC,gBAAgB;gBAAAC,QAAA,gBAC7BvB,OAAA;kBACEyC,IAAI,EAAEpB,OAAO,CAACT,MAAO;kBACrBU,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,eAEvFvB,OAAA;oBAAKsB,SAAS,EAAC,SAAS;oBAACoB,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAApB,QAAA,eAC9DvB,OAAA;sBAAM4C,CAAC,EAAC;oBAA2sB;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACltB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC,eACJlC,OAAA;kBACEyC,IAAI,EAAEpB,OAAO,CAACR,IAAK;kBACnBS,SAAS,EAAC,6EAA6E;kBAAAC,QAAA,eAEvFvB,OAAA;oBAAKsB,SAAS,EAAC,SAAS;oBAACoB,IAAI,EAAC,MAAM;oBAACG,MAAM,EAAC,cAAc;oBAACF,OAAO,EAAC,WAAW;oBAAApB,QAAA,eAC5EvB,OAAA;sBAAM8C,aAAa,EAAC,OAAO;sBAACC,cAAc,EAAC,OAAO;sBAACC,WAAW,EAAE,CAAE;sBAACJ,CAAC,EAAC;oBAA8E;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnJ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNlC,OAAA;YAAKsB,SAAS,EAAC,KAAK;YAAAC,QAAA,gBAClBvB,OAAA;cAAIsB,SAAS,EAAC,kFAAkF;cAAAC,QAAA,EAC7FF,OAAO,CAACd;YAAK;cAAAwB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACLlC,OAAA;cAAGsB,SAAS,EAAC,4CAA4C;cAAAC,QAAA,EACtDF,OAAO,CAACb;YAAW;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,eAGJlC,OAAA;cAAKsB,SAAS,EAAC,sBAAsB;cAAAC,QAAA,EAClCF,OAAO,CAACX,YAAY,CAAC0B,GAAG,CAAEa,IAAI,iBAC7BjD,OAAA;gBAEEsB,SAAS,EAAC,qGAAqG;gBAAAC,QAAA,EAE9G0B;cAAI,GAHAA,IAAI;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAIL,CACP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA,GAnEDb,OAAO,CAACf,EAAE;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoEL,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNlC,OAAA,CAACT,MAAM,CAACiC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BvB,OAAA;UAAGsB,SAAS,EAAC,4BAA4B;UAAAC,QAAA,EAAC;QAE1C;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJlC,OAAA;UACEyC,IAAI,EAAC,oBAAoB;UACzBS,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzB7B,SAAS,EAAC,mKAAmK;UAAAC,QAAA,GAC9K,6BAEC,eAAAvB,OAAA;YAAKsB,SAAS,EAAC,cAAc;YAACoB,IAAI,EAAC,MAAM;YAACG,MAAM,EAAC,cAAc;YAACF,OAAO,EAAC,WAAW;YAAApB,QAAA,eACjFvB,OAAA;cAAM8C,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACJ,CAAC,EAAC;YAA0B;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/F,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAChC,EAAA,CA9NID,QAAQ;AAAAmD,EAAA,GAARnD,QAAQ;AAgOd,eAAeA,QAAQ;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}