import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaGithub, FaExternalLinkAlt, FaCode, FaEye, FaStar, FaCalendarAlt } from 'react-icons/fa';

const Projects = () => {
  const [filter, setFilter] = useState('all');
  const [hoveredProject, setHoveredProject] = useState(null);

  const projects = [
    {
      id: 1,
      title: 'E-Commerce Platform',
      description: 'A comprehensive full-stack e-commerce solution featuring modern UI/UX, secure payment processing, real-time inventory management, and advanced analytics dashboard.',
      longDescription: 'Built with React and Node.js, this platform handles thousands of products and users. Features include JWT authentication, Stripe payments, email notifications, and a powerful admin panel with sales analytics.',
      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',
      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'JWT', 'Socket.io'],
      category: 'fullstack',
      github: '#',
      demo: '#',
      featured: true,
      status: 'Live',
      date: '2023',
      stats: { stars: 124, forks: 45, views: '2.1k' }
    },
    {
      id: 2,
      title: 'AI-Powered Task Manager',
      description: 'An intelligent task management application with AI-driven priority suggestions, real-time collaboration, and advanced analytics.',
      longDescription: 'Features include drag-and-drop Kanban boards, AI task prioritization, team chat, file sharing, and detailed productivity analytics. Built with modern React patterns and Firebase real-time database.',
      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop',
      technologies: ['React', 'Firebase', 'OpenAI API', 'Material-UI', 'Chart.js'],
      category: 'frontend',
      github: '#',
      demo: '#',
      featured: true,
      status: 'In Development',
      date: '2023',
      stats: { stars: 89, forks: 23, views: '1.5k' }
    },
    {
      id: 3,
      title: 'Real-Time Weather Analytics',
      description: 'A comprehensive weather dashboard with predictive analytics, interactive maps, and personalized weather insights.',
      longDescription: 'Integrates multiple weather APIs to provide accurate forecasts, severe weather alerts, and climate trend analysis. Features include location-based services, data visualization, and mobile-responsive design.',
      image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=800&h=600&fit=crop',
      technologies: ['React', 'OpenWeather API', 'D3.js', 'Mapbox', 'PWA'],
      category: 'frontend',
      github: '#',
      demo: '#',
      featured: false,
      status: 'Live',
      date: '2023',
      stats: { stars: 67, forks: 18, views: '980' }
    },
    {
      id: 4,
      title: 'Microservices API Gateway',
      description: 'A scalable REST API gateway with microservices architecture, advanced security, and comprehensive monitoring.',
      longDescription: 'Enterprise-grade API server with JWT authentication, rate limiting, request/response logging, API documentation, and health monitoring. Supports multiple databases and caching strategies.',
      image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?w=800&h=600&fit=crop',
      technologies: ['Node.js', 'Express', 'Redis', 'Docker', 'Swagger', 'JWT'],
      category: 'backend',
      github: '#',
      demo: '#',
      featured: true,
      status: 'Live',
      date: '2022',
      stats: { stars: 156, forks: 67, views: '3.2k' }
    },
    {
      id: 5,
      title: 'Social Media Analytics Hub',
      description: 'An advanced social media analytics platform with AI-powered insights, automated reporting, and multi-platform integration.',
      longDescription: 'Comprehensive dashboard that aggregates data from multiple social platforms, provides sentiment analysis, engagement metrics, and automated report generation. Features real-time data processing and predictive analytics.',
      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',
      technologies: ['React', 'D3.js', 'Node.js', 'PostgreSQL', 'Python', 'TensorFlow'],
      category: 'fullstack',
      github: '#',
      demo: '#',
      featured: false,
      status: 'Live',
      date: '2023',
      stats: { stars: 92, forks: 34, views: '1.8k' }
    },
    {
      id: 6,
      title: 'Cloud-Native Mobile Backend',
      description: 'Highly scalable serverless backend infrastructure with real-time messaging, push notifications, and global CDN.',
      longDescription: 'Built on AWS Lambda and DynamoDB, this backend supports millions of users with real-time chat, file uploads, push notifications, and advanced caching. Includes comprehensive monitoring and auto-scaling.',
      image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',
      technologies: ['Node.js', 'AWS Lambda', 'DynamoDB', 'Socket.io', 'Redis', 'CloudFront'],
      category: 'backend',
      github: '#',
      demo: '#',
      featured: false,
      status: 'Live',
      date: '2022',
      stats: { stars: 78, forks: 29, views: '1.2k' }
    }
  ];

  const categories = [
    { id: 'all', name: 'All Projects', count: projects.length },
    { id: 'fullstack', name: 'Full Stack', count: projects.filter(p => p.category === 'fullstack').length },
    { id: 'frontend', name: 'Frontend', count: projects.filter(p => p.category === 'frontend').length },
    { id: 'backend', name: 'Backend', count: projects.filter(p => p.category === 'backend').length }
  ];

  const filteredProjects = filter === 'all' 
    ? projects 
    : projects.filter(project => project.category === filter);

  return (
    <section id="projects" className="py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-1/3 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/5 to-purple-600/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.span
            className="inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-600 text-sm font-medium mb-4"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            💼 My Portfolio
          </motion.span>

          <h2 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            Featured
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent"> Projects</span>
          </h2>

          <motion.div
            className="w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          />

          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A curated selection of my best work showcasing technical expertise, creative problem-solving, and modern development practices
          </p>
        </motion.div>

        {/* Filter Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex flex-wrap justify-center gap-4 mb-12"
        >
          {categories.map((category, index) => (
            <motion.button
              key={category.id}
              onClick={() => setFilter(category.id)}
              className={`relative px-6 py-3 rounded-full font-semibold transition-all duration-300 ${
                filter === category.id
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                  : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:shadow-lg border border-gray-200'
              }`}
              whileHover={{ scale: 1.05, y: -2 }}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              {category.name}
              <span className={`ml-2 px-2 py-1 rounded-full text-xs ${
                filter === category.id
                  ? 'bg-white/20 text-white'
                  : 'bg-blue-100 text-blue-600'
              }`}>
                {category.count}
              </span>

              {filter === category.id && (
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full"
                  layoutId="activeFilter"
                  transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                />
              )}
            </motion.button>
          ))}
        </motion.div>

        {/* Projects Grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={filter}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            {filteredProjects.map((project, index) => (
              <motion.div
                key={project.id}
                initial={{ opacity: 0, y: 30, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -30, scale: 0.9 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ y: -10, scale: 1.02 }}
                className="group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100"
                onMouseEnter={() => setHoveredProject(project.id)}
                onMouseLeave={() => setHoveredProject(null)}
              >
                {/* Status and Featured badges */}
                <div className="absolute top-4 left-4 z-20 flex gap-2">
                  {project.featured && (
                    <motion.span
                      className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg"
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ delay: 0.3 }}
                    >
                      ⭐ Featured
                    </motion.span>
                  )}
                  <span className={`px-3 py-1 rounded-full text-sm font-medium shadow-lg ${
                    project.status === 'Live'
                      ? 'bg-green-500 text-white'
                      : 'bg-blue-500 text-white'
                  }`}>
                    {project.status}
                  </span>
                </div>

                {/* Project Image */}
                <div className="relative h-56 overflow-hidden">
                  <motion.img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover"
                    whileHover={{ scale: 1.1 }}
                    transition={{ duration: 0.6 }}
                  />

                  {/* Gradient overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Stats overlay */}
                  <motion.div
                    className="absolute top-4 right-4 flex gap-2"
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: hoveredProject === project.id ? 1 : 0, x: hoveredProject === project.id ? 0 : 20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs flex items-center gap-1">
                      <FaStar className="text-yellow-400" />
                      {project.stats.stars}
                    </div>
                    <div className="bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs flex items-center gap-1">
                      <FaEye />
                      {project.stats.views}
                    </div>
                  </motion.div>

                  {/* Action buttons */}
                  <motion.div
                    className="absolute inset-0 flex items-center justify-center gap-4"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{
                      opacity: hoveredProject === project.id ? 1 : 0,
                      y: hoveredProject === project.id ? 0 : 20
                    }}
                    transition={{ duration: 0.3 }}
                  >
                    <motion.a
                      href={project.github}
                      className="p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors shadow-lg"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <FaGithub className="w-5 h-5" />
                    </motion.a>
                    <motion.a
                      href={project.demo}
                      className="p-3 bg-blue-600 rounded-full text-white hover:bg-blue-700 transition-colors shadow-lg"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      <FaExternalLinkAlt className="w-5 h-5" />
                    </motion.a>
                  </motion.div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <div className="flex items-start justify-between mb-3">
                    <h3 className="text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors flex-1">
                      {project.title}
                    </h3>
                    <div className="flex items-center gap-1 text-sm text-gray-500">
                      <FaCalendarAlt className="w-3 h-3" />
                      {project.date}
                    </div>
                  </div>

                  <p className="text-gray-600 mb-4 text-sm leading-relaxed">
                    {hoveredProject === project.id && project.longDescription
                      ? project.longDescription
                      : project.description}
                  </p>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {project.technologies.map((tech, techIndex) => (
                      <motion.span
                        key={tech}
                        className="px-3 py-1 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 rounded-full text-xs font-medium border border-blue-100 hover:border-blue-300 transition-colors"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: techIndex * 0.05 }}
                        whileHover={{ scale: 1.05 }}
                      >
                        {tech}
                      </motion.span>
                    ))}
                  </div>

                  {/* Project stats */}
                  <div className="flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-100">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <FaStar className="text-yellow-500" />
                        <span>{project.stats.stars}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FaCode />
                        <span>{project.stats.forks}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <FaEye />
                        <span>{project.stats.views}</span>
                      </div>
                    </div>
                    <div className="text-xs text-gray-400">
                      {project.category}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center mt-20"
        >
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-12 border border-blue-100">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <h3 className="text-3xl font-bold text-gray-900 mb-4">
                Ready to work together?
              </h3>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
                I'm always excited to take on new challenges and collaborate on innovative projects.
                Let's discuss how we can bring your ideas to life!
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <motion.a
                  href="https://github.com"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-xl transition-all duration-300 group"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <FaGithub className="w-5 h-5 mr-2" />
                  View All Projects
                  <motion.div
                    className="ml-2"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    →
                  </motion.div>
                </motion.a>

                <motion.a
                  href="#contact"
                  className="inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-full font-semibold hover:border-blue-600 hover:text-blue-600 transition-all duration-300"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Get In Touch
                  <FaExternalLinkAlt className="w-4 h-4 ml-2" />
                </motion.a>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Projects;
