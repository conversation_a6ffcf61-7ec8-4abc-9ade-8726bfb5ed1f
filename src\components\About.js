import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaGraduationCap, FaBriefcase, FaAward, FaHeart, FaCode, FaRocket, FaUsers, FaLightbulb } from 'react-icons/fa';

const About = () => {
  const [activeTab, setActiveTab] = useState('story');

  const stats = [
    { number: '5+', label: 'Years Experience', icon: FaRocket, color: 'from-blue-500 to-cyan-500' },
    { number: '50+', label: 'Projects Completed', icon: FaBriefcase, color: 'from-green-500 to-emerald-500' },
    { number: '15+', label: 'Technologies', icon: FaCode, color: 'from-purple-500 to-pink-500' },
    { number: '100%', label: 'Client Satisfaction', icon: FaHeart, color: 'from-red-500 to-rose-500' }
  ];

  const timeline = [
    {
      year: '2023',
      title: 'Senior Full Stack Developer',
      company: 'Tech Innovations Inc.',
      description: 'Leading development of enterprise-scale applications, mentoring junior developers, and architecting scalable solutions.',
      icon: FaBriefcase,
      type: 'work'
    },
    {
      year: '2022',
      title: 'AWS Certified Developer',
      company: 'Amazon Web Services',
      description: 'Achieved AWS certification demonstrating expertise in cloud architecture and serverless technologies.',
      icon: FaAward,
      type: 'achievement'
    },
    {
      year: '2021',
      title: 'Full Stack Developer',
      company: 'Digital Solutions Ltd.',
      description: 'Developed and maintained multiple client projects using React, Node.js, and cloud technologies.',
      icon: FaBriefcase,
      type: 'work'
    },
    {
      year: '2020',
      title: 'Computer Science Degree',
      company: 'University of Technology',
      description: 'Graduated with honors, specializing in software engineering and web technologies.',
      icon: FaGraduationCap,
      type: 'education'
    }
  ];

  const values = [
    {
      icon: FaLightbulb,
      title: 'Innovation',
      description: 'Always exploring new technologies and creative solutions to complex problems.'
    },
    {
      icon: FaUsers,
      title: 'Collaboration',
      description: 'Believing in the power of teamwork and open communication to achieve great results.'
    },
    {
      icon: FaRocket,
      title: 'Excellence',
      description: 'Committed to delivering high-quality code and exceptional user experiences.'
    },
    {
      icon: FaHeart,
      title: 'Passion',
      description: 'Genuinely passionate about technology and its potential to make a positive impact.'
    }
  ];

  const tabs = [
    { id: 'story', label: 'My Story', icon: FaHeart },
    { id: 'journey', label: 'Journey', icon: FaRocket },
    { id: 'values', label: 'Values', icon: FaLightbulb }
  ];

  return (
    <section id="about" className="py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.span
            className="inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            👋 Get to know me
          </motion.span>

          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            About
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"> Me</span>
          </h2>

          <motion.div
            className="w-20 h-1 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          />

          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            Passionate developer, creative problem solver, and technology enthusiast dedicated to building exceptional digital experiences
          </p>
        </motion.div>

        {/* Stats Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-20"
        >
          {stats.map((stat, index) => {
            const IconComponent = stat.icon;
            return (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-2xl p-6 text-center hover:border-blue-500/50 transition-all duration-300 group"
              >
                <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
                <div className="text-3xl font-bold text-white mb-2">{stat.number}</div>
                <div className="text-sm text-gray-400 font-medium">{stat.label}</div>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="flex justify-center mb-12"
        >
          <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-full p-2 shadow-xl">
            {tabs.map((tab, index) => {
              const IconComponent = tab.icon;
              return (
                <motion.button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`relative px-6 py-3 rounded-full font-semibold transition-all duration-300 flex items-center gap-2 ${
                    activeTab === tab.id
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                      : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <IconComponent className="w-4 h-4" />
                  {tab.label}

                  {activeTab === tab.id && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full"
                      layoutId="activeAboutTab"
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </motion.button>
              );
            })}
          </div>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-3xl p-8 mb-20"
          >
            {activeTab === 'story' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h3 className="text-3xl font-bold text-white mb-6">
                    Full Stack Developer & Creative Problem Solver
                  </h3>

                  <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                    I'm a passionate full-stack developer with over 5 years of experience creating digital solutions that make a real difference. My journey in tech started with curiosity about how websites work and has evolved into a career dedicated to building applications that are both beautiful and functional.
                  </p>

                  <p className="text-lg text-gray-300 mb-6 leading-relaxed">
                    I specialize in modern web technologies including React, Node.js, and cloud platforms. I believe in writing clean, maintainable code and creating user experiences that delight and inspire. Every project is an opportunity to learn something new and push the boundaries of what's possible.
                  </p>

                  <p className="text-lg text-gray-300 leading-relaxed">
                    When I'm not coding, you'll find me exploring new technologies, contributing to open-source projects, or mentoring aspiring developers. I'm always excited to take on new challenges and collaborate with teams that share my passion for innovation.
                  </p>
                </div>

                <div className="relative">
                  <motion.div
                    className="w-80 h-80 mx-auto bg-gradient-to-br from-gray-800 to-gray-900 rounded-full border-4 border-gray-700 overflow-hidden shadow-2xl"
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.3 }}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-600/20" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="text-8xl text-gray-600">👨‍💻</div>
                    </div>
                  </motion.div>
                </div>
              </div>
            )}

            {activeTab === 'journey' && (
              <div>
                <h3 className="text-3xl font-bold text-white mb-8 text-center">My Professional Journey</h3>
                <div className="space-y-8">
                  {timeline.map((item, index) => {
                    const IconComponent = item.icon;
                    return (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, x: -50 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.2 }}
                        className="flex items-start gap-6 group"
                      >
                        <div className={`w-16 h-16 rounded-full flex items-center justify-center flex-shrink-0 ${
                          item.type === 'work' ? 'bg-blue-500' :
                          item.type === 'education' ? 'bg-green-500' : 'bg-purple-500'
                        } group-hover:scale-110 transition-transform duration-300`}>
                          <IconComponent className="w-8 h-8 text-white" />
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center gap-4 mb-2">
                            <span className="text-sm font-bold text-blue-400 bg-blue-500/20 px-3 py-1 rounded-full">
                              {item.year}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              item.type === 'work' ? 'bg-blue-500/20 text-blue-400' :
                              item.type === 'education' ? 'bg-green-500/20 text-green-400' : 'bg-purple-500/20 text-purple-400'
                            }`}>
                              {item.type}
                            </span>
                          </div>
                          <h4 className="text-xl font-bold text-white mb-1">{item.title}</h4>
                          <p className="text-blue-400 mb-3 font-medium">{item.company}</p>
                          <p className="text-gray-300 leading-relaxed">{item.description}</p>
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            )}

            {activeTab === 'values' && (
              <div>
                <h3 className="text-3xl font-bold text-white mb-8 text-center">What Drives Me</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {values.map((value, index) => {
                    const IconComponent = value.icon;
                    return (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 30 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.2 }}
                        whileHover={{ scale: 1.05, y: -5 }}
                        className="bg-gray-700/30 border border-gray-600 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 group"
                      >
                        <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                          <IconComponent className="w-6 h-6 text-white" />
                        </div>
                        <h4 className="text-xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors">
                          {value.title}
                        </h4>
                        <p className="text-gray-300 leading-relaxed">{value.description}</p>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            )}
          </motion.div>
        </AnimatePresence>

        {/* Technologies */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-20"
        >
          <h3 className="text-3xl font-bold text-white mb-4">
            Technologies I
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"> Master</span>
          </h3>
          <p className="text-gray-400 mb-12 max-w-2xl mx-auto">
            A comprehensive toolkit of modern technologies and frameworks I use to build exceptional digital experiences
          </p>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
            {[
              { name: 'React', color: '#61DAFB' },
              { name: 'Node.js', color: '#339933' },
              { name: 'JavaScript', color: '#F7DF1E' },
              { name: 'TypeScript', color: '#3178C6' },
              { name: 'Python', color: '#3776AB' },
              { name: 'MongoDB', color: '#47A248' },
              { name: 'PostgreSQL', color: '#336791' },
              { name: 'AWS', color: '#FF9900' },
              { name: 'Docker', color: '#2496ED' },
              { name: 'Git', color: '#F05032' }
            ].map((tech, index) => (
              <motion.div
                key={tech.name}
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-gray-800/40 backdrop-blur-sm border border-gray-700 rounded-xl p-4 hover:border-blue-500/50 transition-all duration-300 group"
              >
                <div
                  className="w-12 h-12 rounded-lg flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300"
                  style={{ backgroundColor: `${tech.color}20` }}
                >
                  <div
                    className="w-6 h-6 rounded"
                    style={{ backgroundColor: tech.color }}
                  />
                </div>
                <span className="text-white font-medium group-hover:text-blue-400 transition-colors">
                  {tech.name}
                </span>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Call to action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="text-center"
        >
          <div className="bg-gradient-to-r from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-blue-500/30 rounded-3xl p-12">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2 }}
            >
              <h3 className="text-3xl font-bold text-white mb-4">
                Let's Build Something Amazing Together
              </h3>
              <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
                I'm always excited to collaborate on innovative projects and help bring your ideas to life.
                Whether you're looking for a dedicated team member or a freelance developer, let's connect!
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <motion.a
                  href="#contact"
                  className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-xl transition-all duration-300 group"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Let's Talk
                  <motion.div
                    className="ml-2"
                    animate={{ x: [0, 5, 0] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    →
                  </motion.div>
                </motion.a>

                <motion.a
                  href="#projects"
                  className="inline-flex items-center px-8 py-4 border-2 border-gray-600 text-gray-300 rounded-full font-semibold hover:border-blue-500 hover:text-blue-400 transition-all duration-300"
                  whileHover={{ scale: 1.05, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  View My Work
                  <FaRocket className="w-4 h-4 ml-2" />
                </motion.a>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
