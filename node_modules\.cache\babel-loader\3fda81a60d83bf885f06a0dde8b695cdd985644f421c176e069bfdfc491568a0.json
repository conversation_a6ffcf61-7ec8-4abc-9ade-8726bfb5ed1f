{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\Skills.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, useInView } from 'framer-motion';\nimport { FaReact, FaNodeJs, FaJs, FaPython, FaDatabase, FaAws, FaFigma, FaMobile, FaGitAlt, FaDocker } from 'react-icons/fa';\nimport { SiTypescript, SiTailwindcss, SiMongodb, SiPostgresql } from 'react-icons/si';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Skills = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('technical');\n  const skillCategories = {\n    technical: {\n      title: 'Technical Skills',\n      icon: '💻',\n      skills: [{\n        name: 'React/Next.js',\n        level: 95,\n        icon: '⚛️'\n      }, {\n        name: 'Node.js/Express',\n        level: 90,\n        icon: '🟢'\n      }, {\n        name: 'JavaScript/TypeScript',\n        level: 92,\n        icon: '📜'\n      }, {\n        name: 'Python/Django',\n        level: 85,\n        icon: '🐍'\n      }, {\n        name: 'MongoDB/PostgreSQL',\n        level: 88,\n        icon: '🗄️'\n      }, {\n        name: 'AWS/Cloud Services',\n        level: 82,\n        icon: '☁️'\n      }]\n    },\n    design: {\n      title: 'Design & Tools',\n      icon: '🎨',\n      skills: [{\n        name: 'UI/UX Design',\n        level: 85,\n        icon: '🎯'\n      }, {\n        name: 'Figma/Adobe XD',\n        level: 80,\n        icon: '🎨'\n      }, {\n        name: 'Responsive Design',\n        level: 95,\n        icon: '📱'\n      }, {\n        name: 'CSS/Tailwind',\n        level: 92,\n        icon: '🎭'\n      }, {\n        name: 'Git/GitHub',\n        level: 90,\n        icon: '📚'\n      }, {\n        name: 'Docker/DevOps',\n        level: 75,\n        icon: '🐳'\n      }]\n    },\n    soft: {\n      title: 'Soft Skills',\n      icon: '🤝',\n      skills: [{\n        name: 'Problem Solving',\n        level: 95,\n        icon: '🧩'\n      }, {\n        name: 'Team Collaboration',\n        level: 92,\n        icon: '👥'\n      }, {\n        name: 'Communication',\n        level: 88,\n        icon: '💬'\n      }, {\n        name: 'Project Management',\n        level: 85,\n        icon: '📋'\n      }, {\n        name: 'Leadership',\n        level: 82,\n        icon: '👑'\n      }, {\n        name: 'Adaptability',\n        level: 90,\n        icon: '🔄'\n      }]\n    }\n  };\n  const tabs = Object.keys(skillCategories);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"skills\",\n    className: \"py-20 bg-gradient-to-br from-gray-50 to-blue-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl font-bold text-gray-900 mb-4\",\n          children: \"Skills & Expertise\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"A comprehensive overview of my technical abilities and professional skills\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"flex justify-center mb-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-full p-2 shadow-lg\",\n          children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab),\n            className: `px-6 py-3 rounded-full font-semibold transition-all duration-300 ${activeTab === tab ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md' : 'text-gray-600 hover:text-blue-600'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: skillCategories[tab].icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 17\n            }, this), skillCategories[tab].title]\n          }, tab, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 67,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5\n        },\n        className: \"bg-white rounded-3xl p-8 shadow-xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n          children: skillCategories[activeTab].skills.map((skill, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            className: \"group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl mr-3\",\n                  children: skill.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 110,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-gray-800\",\n                  children: skill.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 111,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500 font-medium\",\n                children: [skill.level, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 108,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 rounded-full h-3 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  width: 0\n                },\n                animate: {\n                  width: `${skill.level}%`\n                },\n                transition: {\n                  duration: 1,\n                  delay: index * 0.1\n                },\n                className: \"h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full relative\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-white opacity-20 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 117,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 116,\n              columnNumber: 17\n            }, this)]\n          }, skill.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this)\n      }, activeTab, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl text-white\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-2\",\n            children: \"Fast Learner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Always eager to learn new technologies and adapt to changing requirements\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl text-white\",\n              children: \"\\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-2\",\n            children: \"Performance Focused\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Committed to writing efficient, scalable code that performs well\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 153,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl text-white\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-2\",\n            children: \"Detail Oriented\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Meticulous attention to detail ensures high-quality deliverables\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"mt-16 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-900 mb-8\",\n          children: \"Certifications & Learning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 176,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap justify-center gap-4\",\n          children: ['AWS Certified Developer', 'React Professional', 'Node.js Certified', 'MongoDB University', 'Google Cloud Platform'].map((cert, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.3,\n              delay: index * 0.1\n            },\n            className: \"px-6 py-3 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-700 font-medium\",\n              children: cert\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)\n          }, cert, false, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n_s(Skills, \"dCefCoDG5TKfgQfpJifGe0JqD0A=\");\n_c = Skills;\nexport default Skills;\nvar _c;\n$RefreshReg$(_c, \"Skills\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "useInView", "FaReact", "FaNodeJs", "FaJs", "FaPython", "FaDatabase", "FaAws", "FaFigma", "FaMobile", "FaGitAlt", "<PERSON>aDock<PERSON>", "SiTypescript", "SiTailwindcss", "SiMongodb", "SiPostgresql", "jsxDEV", "_jsxDEV", "Skills", "_s", "activeTab", "setActiveTab", "skillCategories", "technical", "title", "icon", "skills", "name", "level", "design", "soft", "tabs", "Object", "keys", "id", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "delay", "map", "tab", "onClick", "animate", "skill", "index", "x", "width", "cert", "scale", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/Skills.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, useInView } from 'framer-motion';\nimport { FaReact, FaNodeJs, FaJs, FaPython, FaDatabase, FaAws, FaFigma, FaMobile, FaGitAlt, FaDocker } from 'react-icons/fa';\nimport { SiTypescript, SiTailwindcss, SiMongodb, SiPostgresql } from 'react-icons/si';\n\nconst Skills = () => {\n  const [activeTab, setActiveTab] = useState('technical');\n\n  const skillCategories = {\n    technical: {\n      title: 'Technical Skills',\n      icon: '💻',\n      skills: [\n        { name: 'React/Next.js', level: 95, icon: '⚛️' },\n        { name: 'Node.js/Express', level: 90, icon: '🟢' },\n        { name: 'JavaScript/TypeScript', level: 92, icon: '📜' },\n        { name: 'Python/Django', level: 85, icon: '🐍' },\n        { name: 'MongoDB/PostgreSQL', level: 88, icon: '🗄️' },\n        { name: 'AWS/Cloud Services', level: 82, icon: '☁️' }\n      ]\n    },\n    design: {\n      title: 'Design & Tools',\n      icon: '🎨',\n      skills: [\n        { name: 'UI/UX Design', level: 85, icon: '🎯' },\n        { name: 'Figma/Adobe XD', level: 80, icon: '🎨' },\n        { name: 'Responsive Design', level: 95, icon: '📱' },\n        { name: 'CSS/Tailwind', level: 92, icon: '🎭' },\n        { name: 'Git/GitHub', level: 90, icon: '📚' },\n        { name: 'Docker/DevOps', level: 75, icon: '🐳' }\n      ]\n    },\n    soft: {\n      title: 'Soft Skills',\n      icon: '🤝',\n      skills: [\n        { name: 'Problem Solving', level: 95, icon: '🧩' },\n        { name: 'Team Collaboration', level: 92, icon: '👥' },\n        { name: 'Communication', level: 88, icon: '💬' },\n        { name: 'Project Management', level: 85, icon: '📋' },\n        { name: 'Leadership', level: 82, icon: '👑' },\n        { name: 'Adaptability', level: 90, icon: '🔄' }\n      ]\n    }\n  };\n\n  const tabs = Object.keys(skillCategories);\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-gradient-to-br from-gray-50 to-blue-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl font-bold text-gray-900 mb-4\">Skills & Expertise</h2>\n          <div className=\"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"></div>\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            A comprehensive overview of my technical abilities and professional skills\n          </p>\n        </motion.div>\n\n        {/* Tab Navigation */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex justify-center mb-12\"\n        >\n          <div className=\"bg-white rounded-full p-2 shadow-lg\">\n            {tabs.map((tab) => (\n              <button\n                key={tab}\n                onClick={() => setActiveTab(tab)}\n                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${\n                  activeTab === tab\n                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md'\n                    : 'text-gray-600 hover:text-blue-600'\n                }`}\n              >\n                <span className=\"mr-2\">{skillCategories[tab].icon}</span>\n                {skillCategories[tab].title}\n              </button>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Skills Content */}\n        <motion.div\n          key={activeTab}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"bg-white rounded-3xl p-8 shadow-xl\"\n        >\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            {skillCategories[activeTab].skills.map((skill, index) => (\n              <motion.div\n                key={skill.name}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                className=\"group\"\n              >\n                <div className=\"flex items-center justify-between mb-3\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-2xl mr-3\">{skill.icon}</span>\n                    <span className=\"font-semibold text-gray-800\">{skill.name}</span>\n                  </div>\n                  <span className=\"text-sm text-gray-500 font-medium\">{skill.level}%</span>\n                </div>\n                \n                <div className=\"w-full bg-gray-200 rounded-full h-3 overflow-hidden\">\n                  <motion.div\n                    initial={{ width: 0 }}\n                    animate={{ width: `${skill.level}%` }}\n                    transition={{ duration: 1, delay: index * 0.1 }}\n                    className=\"h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full relative\"\n                  >\n                    <div className=\"absolute inset-0 bg-white opacity-20 rounded-full\"></div>\n                  </motion.div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Additional Info */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\"\n        >\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl text-white\">🚀</span>\n            </div>\n            <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Fast Learner</h3>\n            <p className=\"text-gray-600\">\n              Always eager to learn new technologies and adapt to changing requirements\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl text-white\">⚡</span>\n            </div>\n            <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Performance Focused</h3>\n            <p className=\"text-gray-600\">\n              Committed to writing efficient, scalable code that performs well\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl text-white\">🎯</span>\n            </div>\n            <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Detail Oriented</h3>\n            <p className=\"text-gray-600\">\n              Meticulous attention to detail ensures high-quality deliverables\n            </p>\n          </div>\n        </motion.div>\n\n        {/* Certifications */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          className=\"mt-16 text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">Certifications & Learning</h3>\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {[\n              'AWS Certified Developer',\n              'React Professional',\n              'Node.js Certified',\n              'MongoDB University',\n              'Google Cloud Platform'\n            ].map((cert, index) => (\n              <motion.div\n                key={cert}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3, delay: index * 0.1 }}\n                className=\"px-6 py-3 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow duration-300\"\n              >\n                <span className=\"text-gray-700 font-medium\">{cert}</span>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Skills;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACjD,SAASC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AAC5H,SAASC,YAAY,EAAEC,aAAa,EAAEC,SAAS,EAAEC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtF,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,WAAW,CAAC;EAEvD,MAAMyB,eAAe,GAAG;IACtBC,SAAS,EAAE;MACTC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EAChD;QAAEE,IAAI,EAAE,iBAAiB;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EAClD;QAAEE,IAAI,EAAE,uBAAuB;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EACxD;QAAEE,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EAChD;QAAEE,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAM,CAAC,EACtD;QAAEE,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC;IAEzD,CAAC;IACDI,MAAM,EAAE;MACNL,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EAC/C;QAAEE,IAAI,EAAE,gBAAgB;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EACjD;QAAEE,IAAI,EAAE,mBAAmB;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EACpD;QAAEE,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EAC/C;QAAEE,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EAC7C;QAAEE,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC;IAEpD,CAAC;IACDK,IAAI,EAAE;MACJN,KAAK,EAAE,aAAa;MACpBC,IAAI,EAAE,IAAI;MACVC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,iBAAiB;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EAClD;QAAEE,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EACrD;QAAEE,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EAChD;QAAEE,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EACrD;QAAEE,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC,EAC7C;QAAEE,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,EAAE;QAAEH,IAAI,EAAE;MAAK,CAAC;IAEnD;EACF,CAAC;EAED,MAAMM,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACX,eAAe,CAAC;EAEzC,oBACEL,OAAA;IAASiB,EAAE,EAAC,QAAQ;IAACC,SAAS,EAAC,iDAAiD;IAAAC,QAAA,eAC9EnB,OAAA;MAAKkB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDnB,OAAA,CAACjB,MAAM,CAACqC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BnB,OAAA;UAAIkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAkB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E9B,OAAA;UAAKkB,SAAS,EAAC;QAAoE;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1F9B,OAAA;UAAGkB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb9B,OAAA,CAACjB,MAAM,CAACqC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eAErCnB,OAAA;UAAKkB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjDL,IAAI,CAACkB,GAAG,CAAEC,GAAG,iBACZjC,OAAA;YAEEkC,OAAO,EAAEA,CAAA,KAAM9B,YAAY,CAAC6B,GAAG,CAAE;YACjCf,SAAS,EAAE,oEACTf,SAAS,KAAK8B,GAAG,GACb,mEAAmE,GACnE,mCAAmC,EACtC;YAAAd,QAAA,gBAEHnB,OAAA;cAAMkB,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAEd,eAAe,CAAC4B,GAAG,CAAC,CAACzB;YAAI;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxDzB,eAAe,CAAC4B,GAAG,CAAC,CAAC1B,KAAK;UAAA,GATtB0B,GAAG;YAAAN,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUF,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb9B,OAAA,CAACjB,MAAM,CAACqC,GAAG;QAETC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BY,OAAO,EAAE;UAAEb,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAC9BE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eAE9CnB,OAAA;UAAKkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDd,eAAe,CAACF,SAAS,CAAC,CAACM,MAAM,CAACuB,GAAG,CAAC,CAACI,KAAK,EAAEC,KAAK,kBAClDrC,OAAA,CAACjB,MAAM,CAACqC,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEgB,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCH,OAAO,EAAE;cAAEb,OAAO,EAAE,CAAC;cAAEgB,CAAC,EAAE;YAAE,CAAE;YAC9Bb,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEK,KAAK,EAAEM,KAAK,GAAG;YAAI,CAAE;YAClDnB,SAAS,EAAC,OAAO;YAAAC,QAAA,gBAEjBnB,OAAA;cAAKkB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDnB,OAAA;gBAAKkB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCnB,OAAA;kBAAMkB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEiB,KAAK,CAAC5B;gBAAI;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnD9B,OAAA;kBAAMkB,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEiB,KAAK,CAAC1B;gBAAI;kBAAAiB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACN9B,OAAA;gBAAMkB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAEiB,KAAK,CAACzB,KAAK,EAAC,GAAC;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAEN9B,OAAA;cAAKkB,SAAS,EAAC,qDAAqD;cAAAC,QAAA,eAClEnB,OAAA,CAACjB,MAAM,CAACqC,GAAG;gBACTC,OAAO,EAAE;kBAAEkB,KAAK,EAAE;gBAAE,CAAE;gBACtBJ,OAAO,EAAE;kBAAEI,KAAK,EAAE,GAAGH,KAAK,CAACzB,KAAK;gBAAI,CAAE;gBACtCc,UAAU,EAAE;kBAAEC,QAAQ,EAAE,CAAC;kBAAEK,KAAK,EAAEM,KAAK,GAAG;gBAAI,CAAE;gBAChDnB,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,eAErFnB,OAAA;kBAAKkB,SAAS,EAAC;gBAAmD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA,GAvBDM,KAAK,CAAC1B,IAAI;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC,GAnCD3B,SAAS;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoCJ,CAAC,eAGb9B,OAAA,CAACjB,MAAM,CAACqC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAEvDnB,OAAA;UAAKkB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnB,OAAA;YAAKkB,SAAS,EAAC,mHAAmH;YAAAC,QAAA,eAChInB,OAAA;cAAMkB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACN9B,OAAA;YAAIkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtE9B,OAAA;YAAGkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9B,OAAA;UAAKkB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnB,OAAA;YAAKkB,SAAS,EAAC,kHAAkH;YAAAC,QAAA,eAC/HnB,OAAA;cAAMkB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACN9B,OAAA;YAAIkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAmB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7E9B,OAAA;YAAGkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN9B,OAAA;UAAKkB,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BnB,OAAA;YAAKkB,SAAS,EAAC,mHAAmH;YAAAC,QAAA,eAChInB,OAAA;cAAMkB,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACN9B,OAAA;YAAIkB,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzE9B,OAAA;YAAGkB,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb9B,OAAA,CAACjB,MAAM,CAACqC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEK,KAAK,EAAE;QAAI,CAAE;QAC1Cb,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BnB,OAAA;UAAIkB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAyB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF9B,OAAA;UAAKkB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjD,CACC,yBAAyB,EACzB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,uBAAuB,CACxB,CAACa,GAAG,CAAC,CAACQ,IAAI,EAAEH,KAAK,kBAChBrC,OAAA,CAACjB,MAAM,CAACqC,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEmB,KAAK,EAAE;YAAI,CAAE;YACpCjB,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEmB,KAAK,EAAE;YAAE,CAAE;YACtChB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEK,KAAK,EAAEM,KAAK,GAAG;YAAI,CAAE;YAClDnB,SAAS,EAAC,0FAA0F;YAAAC,QAAA,eAEpGnB,OAAA;cAAMkB,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEqB;YAAI;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC,GANpDU,IAAI;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOC,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC5B,EAAA,CAlMID,MAAM;AAAAyC,EAAA,GAANzC,MAAM;AAoMZ,eAAeA,MAAM;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}