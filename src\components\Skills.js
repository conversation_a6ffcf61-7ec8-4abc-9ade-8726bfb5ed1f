import React, { useState, useRef, useEffect } from 'react';
import { motion, useInView } from 'framer-motion';
import { FaReact, FaNodeJs, FaJs, FaPython, FaDatabase, FaAws, FaFigma, FaMobile, FaGitAlt, FaDocker } from 'react-icons/fa';
import { SiTypescript, SiTailwindcss, SiMongodb, SiPostgresql } from 'react-icons/si';

const Skills = () => {
  const [activeTab, setActiveTab] = useState('technical');
  const [hoveredSkill, setHoveredSkill] = useState(null);
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, threshold: 0.1 });

  const skillCategories = {
    technical: {
      title: 'Technical Skills',
      icon: FaReact,
      color: 'from-blue-500 to-cyan-500',
      skills: [
        { name: 'React/Next.js', level: 95, icon: FaReact, color: '#61DAFB', description: 'Advanced React development with hooks, context, and performance optimization' },
        { name: 'Node.js/Express', level: 90, icon: FaNodeJs, color: '#339933', description: 'Backend development with RESTful APIs and microservices' },
        { name: 'JavaScript/TypeScript', level: 92, icon: SiTypescript, color: '#3178C6', description: 'Modern ES6+ JavaScript and TypeScript for type-safe development' },
        { name: 'Python/Django', level: 85, icon: FaPython, color: '#3776AB', description: 'Full-stack Python development with Django and Flask' },
        { name: 'MongoDB/PostgreSQL', level: 88, icon: SiMongodb, color: '#47A248', description: 'Database design and optimization for both SQL and NoSQL' },
        { name: 'AWS/Cloud Services', level: 82, icon: FaAws, color: '#FF9900', description: 'Cloud infrastructure and serverless architecture' }
      ]
    },
    design: {
      title: 'Design & Tools',
      icon: FaFigma,
      color: 'from-purple-500 to-pink-500',
      skills: [
        { name: 'UI/UX Design', level: 85, icon: FaFigma, color: '#F24E1E', description: 'User-centered design with modern UI/UX principles' },
        { name: 'Figma/Adobe XD', level: 80, icon: FaFigma, color: '#F24E1E', description: 'Professional design tools for prototyping and collaboration' },
        { name: 'Responsive Design', level: 95, icon: FaMobile, color: '#38BDF8', description: 'Mobile-first responsive design across all devices' },
        { name: 'CSS/Tailwind', level: 92, icon: SiTailwindcss, color: '#06B6D4', description: 'Modern CSS with utility-first frameworks' },
        { name: 'Git/GitHub', level: 90, icon: FaGitAlt, color: '#F05032', description: 'Version control and collaborative development workflows' },
        { name: 'Docker/DevOps', level: 75, icon: FaDocker, color: '#2496ED', description: 'Containerization and CI/CD pipeline management' }
      ]
    },
    soft: {
      title: 'Soft Skills',
      icon: '🤝',
      color: 'from-green-500 to-emerald-500',
      skills: [
        { name: 'Problem Solving', level: 95, icon: '🧩', color: '#10B981', description: 'Analytical thinking and creative solution development' },
        { name: 'Team Collaboration', level: 92, icon: '👥', color: '#3B82F6', description: 'Effective teamwork and cross-functional collaboration' },
        { name: 'Communication', level: 88, icon: '💬', color: '#8B5CF6', description: 'Clear technical communication with stakeholders' },
        { name: 'Project Management', level: 85, icon: '📋', color: '#F59E0B', description: 'Agile methodologies and project delivery' },
        { name: 'Leadership', level: 82, icon: '👑', color: '#EF4444', description: 'Technical leadership and mentoring junior developers' },
        { name: 'Adaptability', level: 90, icon: '🔄', color: '#06B6D4', description: 'Quick adaptation to new technologies and methodologies' }
      ]
    }
  };

  const tabs = Object.keys(skillCategories);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <section id="skills" className="py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
        />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10" ref={ref}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <motion.span
            className="inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4"
            initial={{ opacity: 0, scale: 0.8 }}
            whileInView={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
          >
            🚀 My Expertise
          </motion.span>

          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Skills &
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"> Expertise</span>
          </h2>

          <motion.div
            className="w-20 h-1 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto mb-6"
            initial={{ width: 0 }}
            whileInView={{ width: 80 }}
            transition={{ delay: 0.4, duration: 0.8 }}
          />

          <p className="text-xl text-gray-400 max-w-3xl mx-auto">
            A comprehensive showcase of my technical abilities, design skills, and professional expertise
          </p>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="flex justify-center mb-12"
        >
          <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-full p-2 shadow-xl">
            {tabs.map((tab, index) => {
              const IconComponent = skillCategories[tab].icon;
              return (
                <motion.button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`relative px-6 py-3 rounded-full font-semibold transition-all duration-300 flex items-center gap-2 ${
                    activeTab === tab
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                      : 'text-gray-400 hover:text-white hover:bg-gray-700/50'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {typeof IconComponent === 'function' ? (
                    <IconComponent className="w-4 h-4" />
                  ) : (
                    <span>{IconComponent}</span>
                  )}
                  {skillCategories[tab].title}

                  {activeTab === tab && (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full"
                      layoutId="activeTab"
                      transition={{ type: "spring", bounce: 0.2, duration: 0.6 }}
                    />
                  )}
                </motion.button>
              );
            })}
          </div>
        </motion.div>

        {/* Skills Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-gray-800/30 backdrop-blur-sm border border-gray-700 rounded-3xl p-8 shadow-2xl"
        >
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="grid grid-cols-1 md:grid-cols-2 gap-8"
          >
            {skillCategories[activeTab].skills.map((skill, index) => {
              const IconComponent = skill.icon;
              return (
                <motion.div
                  key={skill.name}
                  variants={itemVariants}
                  className="group relative"
                  onMouseEnter={() => setHoveredSkill(skill.name)}
                  onMouseLeave={() => setHoveredSkill(null)}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-600 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-3">
                        {typeof IconComponent === 'function' ? (
                          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500/20 to-purple-500/20 flex items-center justify-center">
                            <IconComponent
                              className="w-5 h-5"
                              style={{ color: skill.color }}
                            />
                          </div>
                        ) : (
                          <div className="w-10 h-10 rounded-lg bg-gradient-to-r from-blue-500/20 to-purple-500/20 flex items-center justify-center text-xl">
                            {skill.icon}
                          </div>
                        )}
                        <div>
                          <span className="font-semibold text-white text-lg">{skill.name}</span>
                          {hoveredSkill === skill.name && skill.description && (
                            <motion.p
                              initial={{ opacity: 0, height: 0 }}
                              animate={{ opacity: 1, height: 'auto' }}
                              exit={{ opacity: 0, height: 0 }}
                              className="text-sm text-gray-400 mt-1"
                            >
                              {skill.description}
                            </motion.p>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <span className="text-2xl font-bold text-white">{skill.level}%</span>
                        <div className="text-xs text-gray-400">Proficiency</div>
                      </div>
                    </div>

                    <div className="relative">
                      <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
                        <motion.div
                          initial={{ width: 0 }}
                          animate={isInView ? { width: `${skill.level}%` } : { width: 0 }}
                          transition={{ duration: 1.5, delay: index * 0.1, ease: "easeOut" }}
                          className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full relative overflow-hidden"
                        >
                          <motion.div
                            className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent"
                            animate={{ x: ['-100%', '100%'] }}
                            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                          />
                        </motion.div>
                      </div>

                      {/* Skill level indicator */}
                      <motion.div
                        className="absolute -top-8 bg-gray-900 text-white text-xs px-2 py-1 rounded shadow-lg"
                        initial={{ opacity: 0, x: 0 }}
                        animate={isInView ? {
                          opacity: hoveredSkill === skill.name ? 1 : 0,
                          x: `${skill.level}%`
                        } : { opacity: 0, x: 0 }}
                        style={{ transform: 'translateX(-50%)' }}
                      >
                        {skill.level}%
                        <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-gray-900"></div>
                      </motion.div>
                    </div>
                  </div>
                </motion.div>
              );
            })}
          </motion.div>
        </motion.div>

        {/* Additional Info */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8"
        >
          {[
            {
              icon: '🚀',
              title: 'Fast Learner',
              description: 'Always eager to learn new technologies and adapt to changing requirements',
              gradient: 'from-blue-500 to-purple-500'
            },
            {
              icon: '⚡',
              title: 'Performance Focused',
              description: 'Committed to writing efficient, scalable code that performs well',
              gradient: 'from-green-500 to-blue-500'
            },
            {
              icon: '🎯',
              title: 'Detail Oriented',
              description: 'Meticulous attention to detail ensures high-quality deliverables',
              gradient: 'from-purple-500 to-pink-500'
            }
          ].map((item, index) => (
            <motion.div
              key={item.title}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.2, duration: 0.6 }}
              whileHover={{ scale: 1.05, y: -5 }}
              className="text-center group"
            >
              <div className="relative">
                <motion.div
                  className={`w-20 h-20 bg-gradient-to-r ${item.gradient} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-shadow duration-300`}
                  whileHover={{ rotate: 5 }}
                >
                  <span className="text-3xl text-white">{item.icon}</span>
                </motion.div>

                {/* Glow effect */}
                <motion.div
                  className={`absolute inset-0 w-20 h-20 bg-gradient-to-r ${item.gradient} rounded-2xl mx-auto opacity-0 group-hover:opacity-20 blur-xl transition-opacity duration-300`}
                />
              </div>

              <h3 className="text-xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors">
                {item.title}
              </h3>
              <p className="text-gray-400 leading-relaxed">
                {item.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Certifications & Achievements */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mt-20 text-center"
        >
          <h3 className="text-3xl font-bold text-white mb-4">
            Certifications &
            <span className="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"> Achievements</span>
          </h3>
          <p className="text-gray-400 mb-12 max-w-2xl mx-auto">
            Continuous learning and professional development through industry-recognized certifications
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              { name: 'AWS Certified Developer', icon: '☁️', level: 'Associate', year: '2023' },
              { name: 'React Professional', icon: '⚛️', level: 'Advanced', year: '2023' },
              { name: 'Node.js Certified', icon: '🟢', level: 'Professional', year: '2022' },
              { name: 'MongoDB University', icon: '🍃', level: 'M001 & M121', year: '2022' },
              { name: 'Google Cloud Platform', icon: '🌐', level: 'Associate', year: '2023' },
              { name: 'Agile & Scrum Master', icon: '🏃‍♂️', level: 'Certified', year: '2021' }
            ].map((cert, index) => (
              <motion.div
                key={cert.name}
                initial={{ opacity: 0, scale: 0.8, y: 20 }}
                whileInView={{ opacity: 1, scale: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="bg-gray-800/40 backdrop-blur-sm border border-gray-700 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 group"
              >
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {cert.icon}
                </div>
                <h4 className="text-lg font-semibold text-white mb-2 group-hover:text-blue-400 transition-colors">
                  {cert.name}
                </h4>
                <div className="flex justify-between items-center text-sm text-gray-400">
                  <span className="bg-blue-500/20 text-blue-400 px-2 py-1 rounded-full">
                    {cert.level}
                  </span>
                  <span>{cert.year}</span>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mt-16 grid grid-cols-2 md:grid-cols-4 gap-8"
          >
            {[
              { number: '50+', label: 'Projects Completed' },
              { number: '5+', label: 'Years Experience' },
              { number: '15+', label: 'Technologies' },
              { number: '100%', label: 'Client Satisfaction' }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, scale: 0.5 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent mb-2">
                  {stat.number}
                </div>
                <div className="text-gray-400 text-sm">{stat.label}</div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Skills;
