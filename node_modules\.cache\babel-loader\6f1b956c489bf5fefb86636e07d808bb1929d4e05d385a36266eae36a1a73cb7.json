{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\About.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGraduationCap, FaBriefcase, FaAward, FaHeart, FaCode, FaRocket, FaUsers, FaLightbulb } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst About = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('story');\n  const stats = [{\n    number: '5+',\n    label: 'Years Experience',\n    icon: FaRocket,\n    color: 'from-blue-500 to-cyan-500'\n  }, {\n    number: '50+',\n    label: 'Projects Completed',\n    icon: FaBriefcase,\n    color: 'from-green-500 to-emerald-500'\n  }, {\n    number: '15+',\n    label: 'Technologies',\n    icon: FaCode,\n    color: 'from-purple-500 to-pink-500'\n  }, {\n    number: '100%',\n    label: 'Client Satisfaction',\n    icon: FaHeart,\n    color: 'from-red-500 to-rose-500'\n  }];\n  const timeline = [{\n    year: '2023',\n    title: 'Senior Full Stack Developer',\n    company: 'Tech Innovations Inc.',\n    description: 'Leading development of enterprise-scale applications, mentoring junior developers, and architecting scalable solutions.',\n    icon: FaBriefcase,\n    type: 'work'\n  }, {\n    year: '2022',\n    title: 'AWS Certified Developer',\n    company: 'Amazon Web Services',\n    description: 'Achieved AWS certification demonstrating expertise in cloud architecture and serverless technologies.',\n    icon: FaAward,\n    type: 'achievement'\n  }, {\n    year: '2021',\n    title: 'Full Stack Developer',\n    company: 'Digital Solutions Ltd.',\n    description: 'Developed and maintained multiple client projects using React, Node.js, and cloud technologies.',\n    icon: FaBriefcase,\n    type: 'work'\n  }, {\n    year: '2020',\n    title: 'Computer Science Degree',\n    company: 'University of Technology',\n    description: 'Graduated with honors, specializing in software engineering and web technologies.',\n    icon: FaGraduationCap,\n    type: 'education'\n  }];\n  const values = [{\n    icon: FaLightbulb,\n    title: 'Innovation',\n    description: 'Always exploring new technologies and creative solutions to complex problems.'\n  }, {\n    icon: FaUsers,\n    title: 'Collaboration',\n    description: 'Believing in the power of teamwork and open communication to achieve great results.'\n  }, {\n    icon: FaRocket,\n    title: 'Excellence',\n    description: 'Committed to delivering high-quality code and exceptional user experiences.'\n  }, {\n    icon: FaHeart,\n    title: 'Passion',\n    description: 'Genuinely passionate about technology and its potential to make a positive impact.'\n  }];\n  const tabs = [{\n    id: 'story',\n    label: 'My Story',\n    icon: FaHeart\n  }, {\n    id: 'journey',\n    label: 'Journey',\n    icon: FaRocket\n  }, {\n    id: 'values',\n    label: 'Values',\n    icon: FaLightbulb\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"about\",\n    className: \"py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full blur-3xl\",\n        animate: {\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.span, {\n          className: \"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          whileInView: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            delay: 0.2\n          },\n          children: \"\\uD83D\\uDC4B Get to know me\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 104,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n          children: [\"About\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n            children: \" Me\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 115,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 113,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"w-20 h-1 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto mb-6\",\n          initial: {\n            width: 0\n          },\n          whileInView: {\n            width: 80\n          },\n          transition: {\n            delay: 0.4,\n            duration: 0.8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 118,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-400 max-w-3xl mx-auto\",\n          children: \"Passionate developer, creative problem solver, and technology enthusiast dedicated to building exceptional digital experiences\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: -50\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-3xl font-bold text-gray-900 mb-6\",\n            children: \"Full Stack Developer & Creative Problem Solver\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-6 leading-relaxed\",\n            children: \"I'm a passionate full-stack developer with over 3 years of experience creating digital solutions that make a difference. My journey in tech started with curiosity and has evolved into a career dedicated to building applications that are both beautiful and functional.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg text-gray-600 mb-8 leading-relaxed\",\n            children: \"I specialize in modern web technologies including React, Node.js, and cloud platforms. I believe in writing clean, maintainable code and creating user experiences that delight and inspire.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: skills.map((skill, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                x: -20\n              },\n              whileInView: {\n                opacity: 1,\n                x: 0\n              },\n              transition: {\n                duration: 0.6,\n                delay: index * 0.1\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-center mb-2\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm font-semibold text-gray-700\",\n                  children: skill.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 164,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-sm text-gray-500\",\n                  children: [skill.level, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 163,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-gray-200 rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    width: 0\n                  },\n                  whileInView: {\n                    width: `${skill.level}%`\n                  },\n                  transition: {\n                    duration: 1,\n                    delay: index * 0.1\n                  },\n                  className: `${skill.color} h-2 rounded-full`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 19\n              }, this)]\n            }, skill.name, true, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            x: 50\n          },\n          whileInView: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-2 gap-6\",\n            children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n              initial: {\n                opacity: 0,\n                scale: 0.5\n              },\n              whileInView: {\n                opacity: 1,\n                scale: 1\n              },\n              transition: {\n                duration: 0.5,\n                delay: index * 0.1\n              },\n              className: \"bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 text-center hover:shadow-lg transition-shadow duration-300\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-4xl mb-4\",\n                children: stat.icon\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-gray-900 mb-2\",\n                children: stat.number\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-sm text-gray-600 font-medium\",\n                children: stat.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 19\n              }, this)]\n            }, stat.label, true, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.4\n            },\n            className: \"mt-8 flex justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-48 h-48 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center shadow-xl\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-6xl\",\n                children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBB\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 204,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-900 mb-8\",\n          children: \"Technologies I Work With\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 224,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap justify-center gap-4\",\n          children: ['React', 'Node.js', 'JavaScript', 'TypeScript', 'Python', 'MongoDB', 'PostgreSQL', 'AWS', 'Docker', 'Git'].map((tech, index) => /*#__PURE__*/_jsxDEV(motion.span, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.3,\n              delay: index * 0.05\n            },\n            className: \"px-6 py-3 bg-gradient-to-r from-blue-50 to-purple-50 text-gray-700 rounded-full font-medium hover:shadow-md transition-shadow duration-300\",\n            children: tech\n          }, tech, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 225,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 218,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"text-center mt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold mb-4\",\n            children: \"Ready to work together?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-lg mb-6 opacity-90\",\n            children: \"Let's create something amazing that makes a difference\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#contact\",\n            className: \"inline-flex items-center px-8 py-3 bg-white text-blue-600 rounded-full font-semibold hover:shadow-lg transition-all duration-300\",\n            children: [\"Get In Touch\", /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 ml-2\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 261,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 5\n  }, this);\n};\n_s(About, \"xrVXYcbQxoKv7AZJ0h61LqKtyPU=\");\n_c = About;\nexport default About;\nvar _c;\n$RefreshReg$(_c, \"About\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "FaGraduationCap", "FaBriefcase", "FaAward", "FaHeart", "FaCode", "FaRocket", "FaUsers", "FaLightbulb", "jsxDEV", "_jsxDEV", "About", "_s", "activeTab", "setActiveTab", "stats", "number", "label", "icon", "color", "timeline", "year", "title", "company", "description", "type", "values", "tabs", "id", "className", "children", "div", "animate", "scale", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "opacity", "y", "whileInView", "span", "delay", "width", "x", "skills", "map", "skill", "index", "name", "level", "stat", "tech", "href", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/About.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGraduationCap, FaBriefcase, FaAward, FaHeart, FaCode, FaRocket, FaUsers, FaLightbulb } from 'react-icons/fa';\n\nconst About = () => {\n  const [activeTab, setActiveTab] = useState('story');\n\n  const stats = [\n    { number: '5+', label: 'Years Experience', icon: FaRocket, color: 'from-blue-500 to-cyan-500' },\n    { number: '50+', label: 'Projects Completed', icon: FaBriefcase, color: 'from-green-500 to-emerald-500' },\n    { number: '15+', label: 'Technologies', icon: FaCode, color: 'from-purple-500 to-pink-500' },\n    { number: '100%', label: 'Client Satisfaction', icon: FaHeart, color: 'from-red-500 to-rose-500' }\n  ];\n\n  const timeline = [\n    {\n      year: '2023',\n      title: 'Senior Full Stack Developer',\n      company: 'Tech Innovations Inc.',\n      description: 'Leading development of enterprise-scale applications, mentoring junior developers, and architecting scalable solutions.',\n      icon: FaBriefcase,\n      type: 'work'\n    },\n    {\n      year: '2022',\n      title: 'AWS Certified Developer',\n      company: 'Amazon Web Services',\n      description: 'Achieved AWS certification demonstrating expertise in cloud architecture and serverless technologies.',\n      icon: FaAward,\n      type: 'achievement'\n    },\n    {\n      year: '2021',\n      title: 'Full Stack Developer',\n      company: 'Digital Solutions Ltd.',\n      description: 'Developed and maintained multiple client projects using React, Node.js, and cloud technologies.',\n      icon: FaBriefcase,\n      type: 'work'\n    },\n    {\n      year: '2020',\n      title: 'Computer Science Degree',\n      company: 'University of Technology',\n      description: 'Graduated with honors, specializing in software engineering and web technologies.',\n      icon: FaGraduationCap,\n      type: 'education'\n    }\n  ];\n\n  const values = [\n    {\n      icon: FaLightbulb,\n      title: 'Innovation',\n      description: 'Always exploring new technologies and creative solutions to complex problems.'\n    },\n    {\n      icon: FaUsers,\n      title: 'Collaboration',\n      description: 'Believing in the power of teamwork and open communication to achieve great results.'\n    },\n    {\n      icon: FaRocket,\n      title: 'Excellence',\n      description: 'Committed to delivering high-quality code and exceptional user experiences.'\n    },\n    {\n      icon: FaHeart,\n      title: 'Passion',\n      description: 'Genuinely passionate about technology and its potential to make a positive impact.'\n    }\n  ];\n\n  const tabs = [\n    { id: 'story', label: 'My Story', icon: FaHeart },\n    { id: 'journey', label: 'Journey', icon: FaRocket },\n    { id: 'values', label: 'Values', icon: FaLightbulb }\n  ];\n\n  return (\n    <section id=\"about\" className=\"py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute bottom-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            className=\"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            👋 Get to know me\n          </motion.span>\n\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            About\n            <span className=\"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\"> Me</span>\n          </h2>\n\n          <motion.div\n            className=\"w-20 h-1 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto mb-6\"\n            initial={{ width: 0 }}\n            whileInView={{ width: 80 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n          />\n\n          <p className=\"text-xl text-gray-400 max-w-3xl mx-auto\">\n            Passionate developer, creative problem solver, and technology enthusiast dedicated to building exceptional digital experiences\n          </p>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20\">\n          {/* Left side - Content */}\n          <motion.div\n            initial={{ opacity: 0, x: -50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <h3 className=\"text-3xl font-bold text-gray-900 mb-6\">\n              Full Stack Developer & Creative Problem Solver\n            </h3>\n            \n            <p className=\"text-lg text-gray-600 mb-6 leading-relaxed\">\n              I'm a passionate full-stack developer with over 3 years of experience \n              creating digital solutions that make a difference. My journey in tech \n              started with curiosity and has evolved into a career dedicated to \n              building applications that are both beautiful and functional.\n            </p>\n            \n            <p className=\"text-lg text-gray-600 mb-8 leading-relaxed\">\n              I specialize in modern web technologies including React, Node.js, and \n              cloud platforms. I believe in writing clean, maintainable code and \n              creating user experiences that delight and inspire.\n            </p>\n\n            {/* Skills bars */}\n            <div className=\"space-y-6\">\n              {skills.map((skill, index) => (\n                <motion.div\n                  key={skill.name}\n                  initial={{ opacity: 0, x: -20 }}\n                  whileInView={{ opacity: 1, x: 0 }}\n                  transition={{ duration: 0.6, delay: index * 0.1 }}\n                >\n                  <div className=\"flex justify-between items-center mb-2\">\n                    <span className=\"text-sm font-semibold text-gray-700\">{skill.name}</span>\n                    <span className=\"text-sm text-gray-500\">{skill.level}%</span>\n                  </div>\n                  <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                    <motion.div\n                      initial={{ width: 0 }}\n                      whileInView={{ width: `${skill.level}%` }}\n                      transition={{ duration: 1, delay: index * 0.1 }}\n                      className={`${skill.color} h-2 rounded-full`}\n                    ></motion.div>\n                  </div>\n                </motion.div>\n              ))}\n            </div>\n          </motion.div>\n\n          {/* Right side - Stats */}\n          <motion.div\n            initial={{ opacity: 0, x: 50 }}\n            whileInView={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"relative\"\n          >\n            <div className=\"grid grid-cols-2 gap-6\">\n              {stats.map((stat, index) => (\n                <motion.div\n                  key={stat.label}\n                  initial={{ opacity: 0, scale: 0.5 }}\n                  whileInView={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5, delay: index * 0.1 }}\n                  className=\"bg-gradient-to-br from-blue-50 to-purple-50 rounded-2xl p-8 text-center hover:shadow-lg transition-shadow duration-300\"\n                >\n                  <div className=\"text-4xl mb-4\">{stat.icon}</div>\n                  <div className=\"text-3xl font-bold text-gray-900 mb-2\">{stat.number}</div>\n                  <div className=\"text-sm text-gray-600 font-medium\">{stat.label}</div>\n                </motion.div>\n              ))}\n            </div>\n\n            {/* Profile image placeholder */}\n            <motion.div\n              initial={{ opacity: 0, scale: 0.8 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.4 }}\n              className=\"mt-8 flex justify-center\"\n            >\n              <div className=\"w-48 h-48 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center shadow-xl\">\n                <div className=\"text-6xl\">👨‍💻</div>\n              </div>\n            </motion.div>\n          </motion.div>\n        </div>\n\n        {/* Technologies */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">Technologies I Work With</h3>\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {[\n              'React', 'Node.js', 'JavaScript', 'TypeScript', 'Python', \n              'MongoDB', 'PostgreSQL', 'AWS', 'Docker', 'Git'\n            ].map((tech, index) => (\n              <motion.span\n                key={tech}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3, delay: index * 0.05 }}\n                className=\"px-6 py-3 bg-gradient-to-r from-blue-50 to-purple-50 text-gray-700 rounded-full font-medium hover:shadow-md transition-shadow duration-300\"\n              >\n                {tech}\n              </motion.span>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Call to action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl p-8 text-white\">\n            <h3 className=\"text-2xl font-bold mb-4\">Ready to work together?</h3>\n            <p className=\"text-lg mb-6 opacity-90\">\n              Let's create something amazing that makes a difference\n            </p>\n            <a\n              href=\"#contact\"\n              className=\"inline-flex items-center px-8 py-3 bg-white text-blue-600 rounded-full font-semibold hover:shadow-lg transition-all duration-300\"\n            >\n              Get In Touch\n              <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n              </svg>\n            </a>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default About;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,eAAe,EAAEC,WAAW,EAAEC,OAAO,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExH,MAAMC,KAAK,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhB,QAAQ,CAAC,OAAO,CAAC;EAEnD,MAAMiB,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,IAAI;IAAEC,KAAK,EAAE,kBAAkB;IAAEC,IAAI,EAAEZ,QAAQ;IAAEa,KAAK,EAAE;EAA4B,CAAC,EAC/F;IAAEH,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE,oBAAoB;IAAEC,IAAI,EAAEhB,WAAW;IAAEiB,KAAK,EAAE;EAAgC,CAAC,EACzG;IAAEH,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE,cAAc;IAAEC,IAAI,EAAEb,MAAM;IAAEc,KAAK,EAAE;EAA8B,CAAC,EAC5F;IAAEH,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE,qBAAqB;IAAEC,IAAI,EAAEd,OAAO;IAAEe,KAAK,EAAE;EAA2B,CAAC,CACnG;EAED,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,uBAAuB;IAChCC,WAAW,EAAE,yHAAyH;IACtIN,IAAI,EAAEhB,WAAW;IACjBuB,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,qBAAqB;IAC9BC,WAAW,EAAE,uGAAuG;IACpHN,IAAI,EAAEf,OAAO;IACbsB,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,sBAAsB;IAC7BC,OAAO,EAAE,wBAAwB;IACjCC,WAAW,EAAE,iGAAiG;IAC9GN,IAAI,EAAEhB,WAAW;IACjBuB,IAAI,EAAE;EACR,CAAC,EACD;IACEJ,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,0BAA0B;IACnCC,WAAW,EAAE,mFAAmF;IAChGN,IAAI,EAAEjB,eAAe;IACrBwB,IAAI,EAAE;EACR,CAAC,CACF;EAED,MAAMC,MAAM,GAAG,CACb;IACER,IAAI,EAAEV,WAAW;IACjBc,KAAK,EAAE,YAAY;IACnBE,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAEX,OAAO;IACbe,KAAK,EAAE,eAAe;IACtBE,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAEZ,QAAQ;IACdgB,KAAK,EAAE,YAAY;IACnBE,WAAW,EAAE;EACf,CAAC,EACD;IACEN,IAAI,EAAEd,OAAO;IACbkB,KAAK,EAAE,SAAS;IAChBE,WAAW,EAAE;EACf,CAAC,CACF;EAED,MAAMG,IAAI,GAAG,CACX;IAAEC,EAAE,EAAE,OAAO;IAAEX,KAAK,EAAE,UAAU;IAAEC,IAAI,EAAEd;EAAQ,CAAC,EACjD;IAAEwB,EAAE,EAAE,SAAS;IAAEX,KAAK,EAAE,SAAS;IAAEC,IAAI,EAAEZ;EAAS,CAAC,EACnD;IAAEsB,EAAE,EAAE,QAAQ;IAAEX,KAAK,EAAE,QAAQ;IAAEC,IAAI,EAAEV;EAAY,CAAC,CACrD;EAED,oBACEE,OAAA;IAASkB,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,6FAA6F;IAAAC,QAAA,gBAEzHpB,OAAA;MAAKmB,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/CpB,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTF,SAAS,EAAC,iHAAiH;QAC3HG,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENjC,OAAA;MAAKmB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEpB,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BP,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BpB,OAAA,CAACX,MAAM,CAACiD,IAAI;UACVnB,SAAS,EAAC,yJAAyJ;UACnKe,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE;UAAI,CAAE;UACpCc,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE;UAAE,CAAE;UACtCE,UAAU,EAAE;YAAEc,KAAK,EAAE;UAAI,CAAE;UAAAnB,QAAA,EAC5B;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAEdjC,OAAA;UAAImB,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAAC,OAE7D,eAAApB,OAAA;YAAMmB,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAAG;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrG,CAAC,eAELjC,OAAA,CAACX,MAAM,CAACgC,GAAG;UACTF,SAAS,EAAC,oEAAoE;UAC9Ee,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBH,WAAW,EAAE;YAAEG,KAAK,EAAE;UAAG,CAAE;UAC3Bf,UAAU,EAAE;YAAEc,KAAK,EAAE,GAAG;YAAEb,QAAQ,EAAE;UAAI;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAEFjC,OAAA;UAAGmB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAEbjC,OAAA;QAAKmB,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBAExEpB,OAAA,CAACX,MAAM,CAACgC,GAAG;UACTa,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE,CAAC;UAAG,CAAE;UAChCJ,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAClChB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAN,QAAA,gBAE9BpB,OAAA;YAAImB,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAELjC,OAAA;YAAGmB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAK1D;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAEJjC,OAAA;YAAGmB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,EAAC;UAI1D;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAGJjC,OAAA;YAAKmB,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBsB,MAAM,CAACC,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvB7C,OAAA,CAACX,MAAM,CAACgC,GAAG;cAETa,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEM,CAAC,EAAE,CAAC;cAAG,CAAE;cAChCJ,WAAW,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEM,CAAC,EAAE;cAAE,CAAE;cAClChB,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEa,KAAK,EAAEM,KAAK,GAAG;cAAI,CAAE;cAAAzB,QAAA,gBAElDpB,OAAA;gBAAKmB,SAAS,EAAC,wCAAwC;gBAAAC,QAAA,gBACrDpB,OAAA;kBAAMmB,SAAS,EAAC,qCAAqC;kBAAAC,QAAA,EAAEwB,KAAK,CAACE;gBAAI;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACzEjC,OAAA;kBAAMmB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,GAAEwB,KAAK,CAACG,KAAK,EAAC,GAAC;gBAAA;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1D,CAAC,eACNjC,OAAA;gBAAKmB,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,eAClDpB,OAAA,CAACX,MAAM,CAACgC,GAAG;kBACTa,OAAO,EAAE;oBAAEM,KAAK,EAAE;kBAAE,CAAE;kBACtBH,WAAW,EAAE;oBAAEG,KAAK,EAAE,GAAGI,KAAK,CAACG,KAAK;kBAAI,CAAE;kBAC1CtB,UAAU,EAAE;oBAAEC,QAAQ,EAAE,CAAC;oBAAEa,KAAK,EAAEM,KAAK,GAAG;kBAAI,CAAE;kBAChD1B,SAAS,EAAE,GAAGyB,KAAK,CAACnC,KAAK;gBAAoB;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA,GAhBDW,KAAK,CAACE,IAAI;cAAAhB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBL,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGbjC,OAAA,CAACX,MAAM,CAACgC,GAAG;UACTa,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAG,CAAE;UAC/BJ,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEM,CAAC,EAAE;UAAE,CAAE;UAClChB,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BP,SAAS,EAAC,UAAU;UAAAC,QAAA,gBAEpBpB,OAAA;YAAKmB,SAAS,EAAC,wBAAwB;YAAAC,QAAA,EACpCf,KAAK,CAACsC,GAAG,CAAC,CAACK,IAAI,EAAEH,KAAK,kBACrB7C,OAAA,CAACX,MAAM,CAACgC,GAAG;cAETa,OAAO,EAAE;gBAAEC,OAAO,EAAE,CAAC;gBAAEZ,KAAK,EAAE;cAAI,CAAE;cACpCc,WAAW,EAAE;gBAAEF,OAAO,EAAE,CAAC;gBAAEZ,KAAK,EAAE;cAAE,CAAE;cACtCE,UAAU,EAAE;gBAAEC,QAAQ,EAAE,GAAG;gBAAEa,KAAK,EAAEM,KAAK,GAAG;cAAI,CAAE;cAClD1B,SAAS,EAAC,wHAAwH;cAAAC,QAAA,gBAElIpB,OAAA;gBAAKmB,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAE4B,IAAI,CAACxC;cAAI;gBAAAsB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAChDjC,OAAA;gBAAKmB,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EAAE4B,IAAI,CAAC1C;cAAM;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1EjC,OAAA;gBAAKmB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAE4B,IAAI,CAACzC;cAAK;gBAAAuB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA,GARhEe,IAAI,CAACzC,KAAK;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASL,CACb;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGNjC,OAAA,CAACX,MAAM,CAACgC,GAAG;YACTa,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAI,CAAE;YACpCc,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAE,CAAE;YACtCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEa,KAAK,EAAE;YAAI,CAAE;YAC1CpB,SAAS,EAAC,0BAA0B;YAAAC,QAAA,eAEpCpB,OAAA;cAAKmB,SAAS,EAAC,iHAAiH;cAAAC,QAAA,eAC9HpB,OAAA;gBAAKmB,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAK;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNjC,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BP,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAEvBpB,OAAA;UAAImB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAwB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACnFjC,OAAA;UAAKmB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjD,CACC,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,EAAE,QAAQ,EACxD,SAAS,EAAE,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE,KAAK,CAChD,CAACuB,GAAG,CAAC,CAACM,IAAI,EAAEJ,KAAK,kBAChB7C,OAAA,CAACX,MAAM,CAACiD,IAAI;YAEVJ,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAI,CAAE;YACpCc,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAE,CAAE;YACtCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEa,KAAK,EAAEM,KAAK,GAAG;YAAK,CAAE;YACnD1B,SAAS,EAAC,4IAA4I;YAAAC,QAAA,EAErJ6B;UAAI,GANAA,IAAI;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOE,CACd;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGbjC,OAAA,CAACX,MAAM,CAACgC,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEa,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAE7BpB,OAAA;UAAKmB,SAAS,EAAC,yEAAyE;UAAAC,QAAA,gBACtFpB,OAAA;YAAImB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAuB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACpEjC,OAAA;YAAGmB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAEvC;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJjC,OAAA;YACEkD,IAAI,EAAC,UAAU;YACf/B,SAAS,EAAC,kIAAkI;YAAAC,QAAA,GAC7I,cAEC,eAAApB,OAAA;cAAKmB,SAAS,EAAC,cAAc;cAACgC,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAjC,QAAA,eACjFpB,OAAA;gBAAMsD,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA0B;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC/B,EAAA,CAxQID,KAAK;AAAAyD,EAAA,GAALzD,KAAK;AA0QX,eAAeA,KAAK;AAAC,IAAAyD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}