{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\Hero.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaGithub, FaLinkedin, FaTwitter, FaDownload, FaArrowRight, FaCode, FaPalette, FaRocket } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Hero = () => {\n  _s();\n  const [currentRole, setCurrentRole] = useState(0);\n  const [text, setText] = useState('');\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [loopNum, setLoopNum] = useState(0);\n  const [typingSpeed, setTypingSpeed] = useState(150);\n  const roles = ['Full Stack Developer', 'Frontend Specialist', 'UI/UX Designer', 'React Expert', 'Problem Solver'];\n\n  // Typewriter effect\n  useEffect(() => {\n    const handleTyping = () => {\n      const i = loopNum % roles.length;\n      const fullText = roles[i];\n      setText(isDeleting ? fullText.substring(0, text.length - 1) : fullText.substring(0, text.length + 1));\n      setTypingSpeed(isDeleting ? 30 : 150);\n      if (!isDeleting && text === fullText) {\n        setTimeout(() => setIsDeleting(true), 500);\n      } else if (isDeleting && text === '') {\n        setIsDeleting(false);\n        setLoopNum(loopNum + 1);\n      }\n    };\n    const timer = setTimeout(handleTyping, typingSpeed);\n    return () => clearTimeout(timer);\n  }, [text, isDeleting, loopNum, typingSpeed, roles]);\n\n  // Simple role rotation for fallback\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentRole(prev => (prev + 1) % roles.length);\n    }, 3000);\n    return () => clearInterval(interval);\n  }, [roles.length]);\n  const scrollToSection = sectionId => {\n    var _document$getElementB;\n    (_document$getElementB = document.getElementById(sectionId)) === null || _document$getElementB === void 0 ? void 0 : _document$getElementB.scrollIntoView({\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"home\",\n    className: \"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-full blur-3xl\",\n        animate: {\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full blur-3xl\",\n        animate: {\n          scale: [1.2, 1, 1.2],\n          rotate: [360, 180, 0]\n        },\n        transition: {\n          duration: 25,\n          repeat: Infinity,\n          ease: \"linear\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this), [...Array(30)].map((_, i) => /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute w-1 h-1 bg-white/20 rounded-full\",\n        style: {\n          left: `${Math.random() * 100}%`,\n          top: `${Math.random() * 100}%`\n        },\n        animate: {\n          y: [0, -100, 0],\n          opacity: [0, 1, 0]\n        },\n        transition: {\n          duration: Math.random() * 10 + 10,\n          repeat: Infinity,\n          delay: Math.random() * 10\n        }\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 11\n      }, this))]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center lg:text-left\",\n          children: [/*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6\n            },\n            children: [/*#__PURE__*/_jsxDEV(motion.p, {\n              className: \"text-blue-600 text-lg font-medium mb-4\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              transition: {\n                delay: 0.2\n              },\n              children: \"Hello, I'm\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n              children: \"Alex Johnson\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-16 mb-8\",\n              children: /*#__PURE__*/_jsxDEV(motion.h2, {\n                className: \"text-2xl md:text-3xl text-gray-600 font-light\",\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: 1,\n                  y: 0\n                },\n                exit: {\n                  opacity: 0,\n                  y: -20\n                },\n                transition: {\n                  duration: 0.5\n                },\n                children: roles[currentRole]\n              }, currentRole, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 130,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.p, {\n              className: \"text-xl text-gray-600 mb-8 max-w-2xl\",\n              initial: {\n                opacity: 0\n              },\n              animate: {\n                opacity: 1\n              },\n              transition: {\n                delay: 0.4\n              },\n              children: \"I create beautiful, functional, and user-centered digital experiences that solve real-world problems with clean, efficient code.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.6\n            },\n            className: \"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\",\n            children: [/*#__PURE__*/_jsxDEV(motion.a, {\n              href: \"#projects\",\n              className: \"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-lg transition-all duration-300\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: [\"View My Work\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5 ml-2\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M17 8l4 4m0 0l-4 4m4-4H3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 167,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n              href: \"#contact\",\n              className: \"inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-full font-semibold hover:border-blue-600 hover:text-blue-600 transition-all duration-300\",\n              whileHover: {\n                scale: 1.05\n              },\n              whileTap: {\n                scale: 0.95\n              },\n              children: \"Get In Touch\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0\n            },\n            animate: {\n              opacity: 1\n            },\n            transition: {\n              duration: 0.6,\n              delay: 0.8\n            },\n            className: \"flex justify-center lg:justify-start space-x-6 mt-12\",\n            children: [{\n              name: 'GitHub',\n              icon: 'M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z'\n            }, {\n              name: 'LinkedIn',\n              icon: 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z'\n            }, {\n              name: 'Twitter',\n              icon: 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z'\n            }].map((social, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n              href: \"#\",\n              className: \"w-12 h-12 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-blue-600 hover:shadow-lg transition-all duration-300\",\n              whileHover: {\n                scale: 1.1,\n                y: -2\n              },\n              whileTap: {\n                scale: 0.9\n              },\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-5 h-5\",\n                fill: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  d: social.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 19\n              }, this)\n            }, social.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"relative\",\n          initial: {\n            opacity: 0,\n            x: 50\n          },\n          animate: {\n            opacity: 1,\n            x: 0\n          },\n          transition: {\n            duration: 0.8,\n            delay: 0.4\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative w-full max-w-lg mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-80 h-80 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto shadow-2xl\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-8xl\",\n                  children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 220,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this), [{\n                name: 'React',\n                color: 'bg-blue-500',\n                position: 'top-4 -left-4'\n              }, {\n                name: 'Node.js',\n                color: 'bg-green-500',\n                position: 'top-16 -right-8'\n              }, {\n                name: 'JavaScript',\n                color: 'bg-yellow-500',\n                position: 'bottom-16 -left-8'\n              }, {\n                name: 'CSS',\n                color: 'bg-purple-500',\n                position: 'bottom-4 -right-4'\n              }].map((tech, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                className: `absolute ${tech.position} ${tech.color} text-white px-4 py-2 rounded-lg text-sm font-semibold shadow-lg`,\n                animate: {\n                  y: [0, -10, 0],\n                  rotate: [0, 5, 0]\n                },\n                transition: {\n                  duration: 3,\n                  repeat: Infinity,\n                  delay: index * 0.5\n                },\n                children: tech.name\n              }, tech.name, false, {\n                fileName: _jsxFileName,\n                lineNumber: 230,\n                columnNumber: 19\n              }, this))]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 218,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute bottom-8 left-1/2 transform -translate-x-1/2\",\n        animate: {\n          y: [0, 10, 0]\n        },\n        transition: {\n          duration: 2,\n          repeat: Infinity\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-1 h-3 bg-gray-400 rounded-full mt-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_s(Hero, \"hcvPJgWEzymSzgcsGRbD3VU17to=\");\n_c = Hero;\nexport default Hero;\nvar _c;\n$RefreshReg$(_c, \"Hero\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaLinkedin", "FaTwitter", "FaDownload", "FaArrowRight", "FaCode", "FaPalette", "FaRocket", "jsxDEV", "_jsxDEV", "Hero", "_s", "currentRole", "setCurrentRole", "text", "setText", "isDeleting", "setIsDeleting", "loopNum", "setLoopNum", "typingSpeed", "setTypingSpeed", "roles", "handleTyping", "i", "length", "fullText", "substring", "setTimeout", "timer", "clearTimeout", "interval", "setInterval", "prev", "clearInterval", "scrollToSection", "sectionId", "_document$getElementB", "document", "getElementById", "scrollIntoView", "behavior", "id", "className", "children", "div", "animate", "scale", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "Array", "map", "_", "style", "left", "Math", "random", "top", "y", "opacity", "delay", "initial", "p", "h2", "exit", "a", "href", "whileHover", "whileTap", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "name", "icon", "social", "index", "x", "color", "position", "tech", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/Hero.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { FaGithub, FaLinkedin, FaTwitter, FaDownload, FaArrowRight, FaCode, FaPalette, FaRocket } from 'react-icons/fa';\n\nconst Hero = () => {\n  const [currentRole, setCurrentRole] = useState(0);\n  const [text, setText] = useState('');\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [loopNum, setLoopNum] = useState(0);\n  const [typingSpeed, setTypingSpeed] = useState(150);\n\n  const roles = [\n    'Full Stack Developer',\n    'Frontend Specialist',\n    'UI/UX Designer',\n    'React Expert',\n    'Problem Solver'\n  ];\n\n  // Typewriter effect\n  useEffect(() => {\n    const handleTyping = () => {\n      const i = loopNum % roles.length;\n      const fullText = roles[i];\n\n      setText(isDeleting\n        ? fullText.substring(0, text.length - 1)\n        : fullText.substring(0, text.length + 1)\n      );\n\n      setTypingSpeed(isDeleting ? 30 : 150);\n\n      if (!isDeleting && text === fullText) {\n        setTimeout(() => setIsDeleting(true), 500);\n      } else if (isDeleting && text === '') {\n        setIsDeleting(false);\n        setLoopNum(loopNum + 1);\n      }\n    };\n\n    const timer = setTimeout(handleTyping, typingSpeed);\n    return () => clearTimeout(timer);\n  }, [text, isDeleting, loopNum, typingSpeed, roles]);\n\n  // Simple role rotation for fallback\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentRole((prev) => (prev + 1) % roles.length);\n    }, 3000);\n    return () => clearInterval(interval);\n  }, [roles.length]);\n\n  const scrollToSection = (sectionId) => {\n    document.getElementById(sectionId)?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  return (\n    <section id=\"home\" className=\"min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900\">\n      {/* Animated background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n        <motion.div\n          className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/20 to-pink-600/20 rounded-full blur-3xl\"\n          animate={{\n            scale: [1.2, 1, 1.2],\n            rotate: [360, 180, 0],\n          }}\n          transition={{\n            duration: 25,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n\n        {/* Floating particles */}\n        {[...Array(30)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-1 h-1 bg-white/20 rounded-full\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              y: [0, -100, 0],\n              opacity: [0, 1, 0],\n            }}\n            transition={{\n              duration: Math.random() * 10 + 10,\n              repeat: Infinity,\n              delay: Math.random() * 10,\n            }}\n          />\n        ))}\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n          {/* Left side - Content */}\n          <div className=\"text-center lg:text-left\">\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6 }}\n            >\n              <motion.p \n                className=\"text-blue-600 text-lg font-medium mb-4\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.2 }}\n              >\n                Hello, I'm\n              </motion.p>\n              \n              <h1 className=\"text-5xl md:text-6xl font-bold text-gray-900 mb-6\">\n                Alex Johnson\n              </h1>\n              \n              <div className=\"h-16 mb-8\">\n                <motion.h2 \n                  key={currentRole}\n                  className=\"text-2xl md:text-3xl text-gray-600 font-light\"\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -20 }}\n                  transition={{ duration: 0.5 }}\n                >\n                  {roles[currentRole]}\n                </motion.h2>\n              </div>\n\n              <motion.p \n                className=\"text-xl text-gray-600 mb-8 max-w-2xl\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.4 }}\n              >\n                I create beautiful, functional, and user-centered digital experiences \n                that solve real-world problems with clean, efficient code.\n              </motion.p>\n            </motion.div>\n\n            <motion.div\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: 0.6 }}\n              className=\"flex flex-col sm:flex-row gap-4 justify-center lg:justify-start\"\n            >\n              <motion.a\n                href=\"#projects\"\n                className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-lg transition-all duration-300\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                View My Work\n                <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 8l4 4m0 0l-4 4m4-4H3\" />\n                </svg>\n              </motion.a>\n              \n              <motion.a\n                href=\"#contact\"\n                className=\"inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-full font-semibold hover:border-blue-600 hover:text-blue-600 transition-all duration-300\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                Get In Touch\n              </motion.a>\n            </motion.div>\n\n            {/* Social links */}\n            <motion.div\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ duration: 0.6, delay: 0.8 }}\n              className=\"flex justify-center lg:justify-start space-x-6 mt-12\"\n            >\n              {[\n                { name: 'GitHub', icon: 'M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z' },\n                { name: 'LinkedIn', icon: 'M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z' },\n                { name: 'Twitter', icon: 'M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z' }\n              ].map((social, index) => (\n                <motion.a\n                  key={social.name}\n                  href=\"#\"\n                  className=\"w-12 h-12 bg-white rounded-full flex items-center justify-center text-gray-600 hover:text-blue-600 hover:shadow-lg transition-all duration-300\"\n                  whileHover={{ scale: 1.1, y: -2 }}\n                  whileTap={{ scale: 0.9 }}\n                >\n                  <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path d={social.icon}/>\n                  </svg>\n                </motion.a>\n              ))}\n            </motion.div>\n          </div>\n\n          {/* Right side - Visual */}\n          <motion.div\n            className=\"relative\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.8, delay: 0.4 }}\n          >\n            <div className=\"relative w-full max-w-lg mx-auto\">\n              {/* Main image container */}\n              <div className=\"relative\">\n                <div className=\"w-80 h-80 bg-gradient-to-br from-blue-100 to-purple-100 rounded-full flex items-center justify-center mx-auto shadow-2xl\">\n                  <div className=\"text-8xl\">👨‍💻</div>\n                </div>\n                \n                {/* Floating elements */}\n                {[\n                  { name: 'React', color: 'bg-blue-500', position: 'top-4 -left-4' },\n                  { name: 'Node.js', color: 'bg-green-500', position: 'top-16 -right-8' },\n                  { name: 'JavaScript', color: 'bg-yellow-500', position: 'bottom-16 -left-8' },\n                  { name: 'CSS', color: 'bg-purple-500', position: 'bottom-4 -right-4' }\n                ].map((tech, index) => (\n                  <motion.div\n                    key={tech.name}\n                    className={`absolute ${tech.position} ${tech.color} text-white px-4 py-2 rounded-lg text-sm font-semibold shadow-lg`}\n                    animate={{\n                      y: [0, -10, 0],\n                      rotate: [0, 5, 0]\n                    }}\n                    transition={{\n                      duration: 3,\n                      repeat: Infinity,\n                      delay: index * 0.5\n                    }}\n                  >\n                    {tech.name}\n                  </motion.div>\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        </div>\n\n        {/* Scroll indicator */}\n        <motion.div\n          className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n          animate={{ y: [0, 10, 0] }}\n          transition={{ duration: 2, repeat: Infinity }}\n        >\n          <div className=\"w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-gray-400 rounded-full mt-2\"></div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Hero;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,QAAQ,EAAEC,UAAU,EAAEC,SAAS,EAAEC,UAAU,EAAEC,YAAY,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExH,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGf,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACgB,IAAI,EAAEC,OAAO,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EACpC,MAAM,CAACkB,UAAU,EAAEC,aAAa,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACoB,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC;EACzC,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,GAAG,CAAC;EAEnD,MAAMwB,KAAK,GAAG,CACZ,sBAAsB,EACtB,qBAAqB,EACrB,gBAAgB,EAChB,cAAc,EACd,gBAAgB,CACjB;;EAED;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM0B,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,CAAC,GAAGN,OAAO,GAAGI,KAAK,CAACG,MAAM;MAChC,MAAMC,QAAQ,GAAGJ,KAAK,CAACE,CAAC,CAAC;MAEzBT,OAAO,CAACC,UAAU,GACdU,QAAQ,CAACC,SAAS,CAAC,CAAC,EAAEb,IAAI,CAACW,MAAM,GAAG,CAAC,CAAC,GACtCC,QAAQ,CAACC,SAAS,CAAC,CAAC,EAAEb,IAAI,CAACW,MAAM,GAAG,CAAC,CACzC,CAAC;MAEDJ,cAAc,CAACL,UAAU,GAAG,EAAE,GAAG,GAAG,CAAC;MAErC,IAAI,CAACA,UAAU,IAAIF,IAAI,KAAKY,QAAQ,EAAE;QACpCE,UAAU,CAAC,MAAMX,aAAa,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC;MAC5C,CAAC,MAAM,IAAID,UAAU,IAAIF,IAAI,KAAK,EAAE,EAAE;QACpCG,aAAa,CAAC,KAAK,CAAC;QACpBE,UAAU,CAACD,OAAO,GAAG,CAAC,CAAC;MACzB;IACF,CAAC;IAED,MAAMW,KAAK,GAAGD,UAAU,CAACL,YAAY,EAAEH,WAAW,CAAC;IACnD,OAAO,MAAMU,YAAY,CAACD,KAAK,CAAC;EAClC,CAAC,EAAE,CAACf,IAAI,EAAEE,UAAU,EAAEE,OAAO,EAAEE,WAAW,EAAEE,KAAK,CAAC,CAAC;;EAEnD;EACAzB,SAAS,CAAC,MAAM;IACd,MAAMkC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCnB,cAAc,CAAEoB,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAIX,KAAK,CAACG,MAAM,CAAC;IACrD,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMS,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACT,KAAK,CAACG,MAAM,CAAC,CAAC;EAElB,MAAMU,eAAe,GAAIC,SAAS,IAAK;IAAA,IAAAC,qBAAA;IACrC,CAAAA,qBAAA,GAAAC,QAAQ,CAACC,cAAc,CAACH,SAAS,CAAC,cAAAC,qBAAA,uBAAlCA,qBAAA,CAAoCG,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAC5E,CAAC;EAED,oBACEhC,OAAA;IAASiC,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,qIAAqI;IAAAC,QAAA,gBAEhKnC,OAAA;MAAKkC,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CnC,OAAA,CAACV,MAAM,CAAC8C,GAAG;QACTF,SAAS,EAAC,8GAA8G;QACxHG,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;QACTF,SAAS,EAAC,kHAAkH;QAC5HG,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC;UACpBC,MAAM,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,CAAC;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAGD,CAAC,GAAGC,KAAK,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEpC,CAAC,kBACvBf,OAAA,CAACV,MAAM,CAAC8C,GAAG;QAETF,SAAS,EAAC,2CAA2C;QACrDkB,KAAK,EAAE;UACLC,IAAI,EAAE,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,GAAG;UAC/BC,GAAG,EAAE,GAAGF,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;QAC7B,CAAE;QACFlB,OAAO,EAAE;UACPoB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;UACfC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;QACnB,CAAE;QACFlB,UAAU,EAAE;UACVC,QAAQ,EAAEa,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;UACjCb,MAAM,EAAEC,QAAQ;UAChBgB,KAAK,EAAEL,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;QACzB;MAAE,GAdGxC,CAAC;QAAA8B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeP,CACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENhD,OAAA;MAAKkC,SAAS,EAAC,4DAA4D;MAAAC,QAAA,gBACzEnC,OAAA;QAAKkC,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAElEnC,OAAA;UAAKkC,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACvCnC,OAAA,CAACV,MAAM,CAAC8C,GAAG;YACTwB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BpB,OAAO,EAAE;cAAEqB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE;YAAI,CAAE;YAAAN,QAAA,gBAE9BnC,OAAA,CAACV,MAAM,CAACuE,CAAC;cACP3B,SAAS,EAAC,wCAAwC;cAClD0B,OAAO,EAAE;gBAAEF,OAAO,EAAE;cAAE,CAAE;cACxBrB,OAAO,EAAE;gBAAEqB,OAAO,EAAE;cAAE,CAAE;cACxBlB,UAAU,EAAE;gBAAEmB,KAAK,EAAE;cAAI,CAAE;cAAAxB,QAAA,EAC5B;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,eAEXhD,OAAA;cAAIkC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAAC;YAElE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAELhD,OAAA;cAAKkC,SAAS,EAAC,WAAW;cAAAC,QAAA,eACxBnC,OAAA,CAACV,MAAM,CAACwE,EAAE;gBAER5B,SAAS,EAAC,+CAA+C;gBACzD0B,OAAO,EAAE;kBAAEF,OAAO,EAAE,CAAC;kBAAED,CAAC,EAAE;gBAAG,CAAE;gBAC/BpB,OAAO,EAAE;kBAAEqB,OAAO,EAAE,CAAC;kBAAED,CAAC,EAAE;gBAAE,CAAE;gBAC9BM,IAAI,EAAE;kBAAEL,OAAO,EAAE,CAAC;kBAAED,CAAC,EAAE,CAAC;gBAAG,CAAE;gBAC7BjB,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAN,QAAA,EAE7BtB,KAAK,CAACV,WAAW;cAAC,GAPdA,WAAW;gBAAA0C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAQP;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAENhD,OAAA,CAACV,MAAM,CAACuE,CAAC;cACP3B,SAAS,EAAC,sCAAsC;cAChD0B,OAAO,EAAE;gBAAEF,OAAO,EAAE;cAAE,CAAE;cACxBrB,OAAO,EAAE;gBAAEqB,OAAO,EAAE;cAAE,CAAE;cACxBlB,UAAU,EAAE;gBAAEmB,KAAK,EAAE;cAAI,CAAE;cAAAxB,QAAA,EAC5B;YAGD;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAEbhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;YACTwB,OAAO,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAG,CAAE;YAC/BpB,OAAO,EAAE;cAAEqB,OAAO,EAAE,CAAC;cAAED,CAAC,EAAE;YAAE,CAAE;YAC9BjB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEkB,KAAK,EAAE;YAAI,CAAE;YAC1CzB,SAAS,EAAC,iEAAiE;YAAAC,QAAA,gBAE3EnC,OAAA,CAACV,MAAM,CAAC0E,CAAC;cACPC,IAAI,EAAC,WAAW;cAChB/B,SAAS,EAAC,mKAAmK;cAC7KgC,UAAU,EAAE;gBAAE5B,KAAK,EAAE;cAAK,CAAE;cAC5B6B,QAAQ,EAAE;gBAAE7B,KAAK,EAAE;cAAK,CAAE;cAAAH,QAAA,GAC3B,cAEC,eAAAnC,OAAA;gBAAKkC,SAAS,EAAC,cAAc;gBAACkC,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAnC,QAAA,eACjFnC,OAAA;kBAAMuE,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA0B;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/F,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eAEXhD,OAAA,CAACV,MAAM,CAAC0E,CAAC;cACPC,IAAI,EAAC,UAAU;cACf/B,SAAS,EAAC,4KAA4K;cACtLgC,UAAU,EAAE;gBAAE5B,KAAK,EAAE;cAAK,CAAE;cAC5B6B,QAAQ,EAAE;gBAAE7B,KAAK,EAAE;cAAK,CAAE;cAAAH,QAAA,EAC3B;YAED;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGbhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;YACTwB,OAAO,EAAE;cAAEF,OAAO,EAAE;YAAE,CAAE;YACxBrB,OAAO,EAAE;cAAEqB,OAAO,EAAE;YAAE,CAAE;YACxBlB,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEkB,KAAK,EAAE;YAAI,CAAE;YAC1CzB,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAE/D,CACC;cAAEwC,IAAI,EAAE,QAAQ;cAAEC,IAAI,EAAE;YAA4sB,CAAC,EACruB;cAAED,IAAI,EAAE,UAAU;cAAEC,IAAI,EAAE;YAAqf,CAAC,EAChhB;cAAED,IAAI,EAAE,SAAS;cAAEC,IAAI,EAAE;YAA8e,CAAC,CACzgB,CAAC1B,GAAG,CAAC,CAAC2B,MAAM,EAAEC,KAAK,kBAClB9E,OAAA,CAACV,MAAM,CAAC0E,CAAC;cAEPC,IAAI,EAAC,GAAG;cACR/B,SAAS,EAAC,gJAAgJ;cAC1JgC,UAAU,EAAE;gBAAE5B,KAAK,EAAE,GAAG;gBAAEmB,CAAC,EAAE,CAAC;cAAE,CAAE;cAClCU,QAAQ,EAAE;gBAAE7B,KAAK,EAAE;cAAI,CAAE;cAAAH,QAAA,eAEzBnC,OAAA;gBAAKkC,SAAS,EAAC,SAAS;gBAACkC,IAAI,EAAC,cAAc;gBAACE,OAAO,EAAC,WAAW;gBAAAnC,QAAA,eAC9DnC,OAAA;kBAAM0E,CAAC,EAAEG,MAAM,CAACD;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC,GARD6B,MAAM,CAACF,IAAI;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OASR,CACX;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;UACTF,SAAS,EAAC,UAAU;UACpB0B,OAAO,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEqB,CAAC,EAAE;UAAG,CAAE;UAC/B1C,OAAO,EAAE;YAAEqB,OAAO,EAAE,CAAC;YAAEqB,CAAC,EAAE;UAAE,CAAE;UAC9BvC,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEkB,KAAK,EAAE;UAAI,CAAE;UAAAxB,QAAA,eAE1CnC,OAAA;YAAKkC,SAAS,EAAC,kCAAkC;YAAAC,QAAA,eAE/CnC,OAAA;cAAKkC,SAAS,EAAC,UAAU;cAAAC,QAAA,gBACvBnC,OAAA;gBAAKkC,SAAS,EAAC,0HAA0H;gBAAAC,QAAA,eACvInC,OAAA;kBAAKkC,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAK;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC,EAGL,CACC;gBAAE2B,IAAI,EAAE,OAAO;gBAAEK,KAAK,EAAE,aAAa;gBAAEC,QAAQ,EAAE;cAAgB,CAAC,EAClE;gBAAEN,IAAI,EAAE,SAAS;gBAAEK,KAAK,EAAE,cAAc;gBAAEC,QAAQ,EAAE;cAAkB,CAAC,EACvE;gBAAEN,IAAI,EAAE,YAAY;gBAAEK,KAAK,EAAE,eAAe;gBAAEC,QAAQ,EAAE;cAAoB,CAAC,EAC7E;gBAAEN,IAAI,EAAE,KAAK;gBAAEK,KAAK,EAAE,eAAe;gBAAEC,QAAQ,EAAE;cAAoB,CAAC,CACvE,CAAC/B,GAAG,CAAC,CAACgC,IAAI,EAAEJ,KAAK,kBAChB9E,OAAA,CAACV,MAAM,CAAC8C,GAAG;gBAETF,SAAS,EAAE,YAAYgD,IAAI,CAACD,QAAQ,IAAIC,IAAI,CAACF,KAAK,kEAAmE;gBACrH3C,OAAO,EAAE;kBACPoB,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC;kBACdlB,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;gBAClB,CAAE;gBACFC,UAAU,EAAE;kBACVC,QAAQ,EAAE,CAAC;kBACXC,MAAM,EAAEC,QAAQ;kBAChBgB,KAAK,EAAEmB,KAAK,GAAG;gBACjB,CAAE;gBAAA3C,QAAA,EAED+C,IAAI,CAACP;cAAI,GAZLO,IAAI,CAACP,IAAI;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAaJ,CACb,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAGNhD,OAAA,CAACV,MAAM,CAAC8C,GAAG;QACTF,SAAS,EAAC,uDAAuD;QACjEG,OAAO,EAAE;UAAEoB,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC;QAAE,CAAE;QAC3BjB,UAAU,EAAE;UAAEC,QAAQ,EAAE,CAAC;UAAEC,MAAM,EAAEC;QAAS,CAAE;QAAAR,QAAA,eAE9CnC,OAAA;UAAKkC,SAAS,EAAC,oEAAoE;UAAAC,QAAA,eACjFnC,OAAA;YAAKkC,SAAS,EAAC;UAAuC;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC9C,EAAA,CAnQID,IAAI;AAAAkF,EAAA,GAAJlF,IAAI;AAqQV,eAAeA,IAAI;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}