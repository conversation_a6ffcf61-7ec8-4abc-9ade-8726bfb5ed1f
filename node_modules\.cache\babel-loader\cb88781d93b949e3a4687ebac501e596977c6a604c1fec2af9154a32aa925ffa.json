{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\Skills.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { motion, useInView } from 'framer-motion';\nimport { FaReact, FaNodeJs, FaJs, FaPython, FaDatabase, FaAws, FaFigma, FaMobile, FaGitAlt, FaDocker } from 'react-icons/fa';\nimport { SiTypescript, SiTailwindcss, SiMongodb, SiPostgresql } from 'react-icons/si';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Skills = () => {\n  _s();\n  const [activeTab, setActiveTab] = useState('technical');\n  const [hoveredSkill, setHoveredSkill] = useState(null);\n  const ref = useRef(null);\n  const isInView = useInView(ref, {\n    once: true,\n    threshold: 0.1\n  });\n  const skillCategories = {\n    technical: {\n      title: 'Technical Skills',\n      icon: FaReact,\n      color: 'from-blue-500 to-cyan-500',\n      skills: [{\n        name: 'React/Next.js',\n        level: 95,\n        icon: FaReact,\n        color: '#61DAFB',\n        description: 'Advanced React development with hooks, context, and performance optimization'\n      }, {\n        name: 'Node.js/Express',\n        level: 90,\n        icon: FaNodeJs,\n        color: '#339933',\n        description: 'Backend development with RESTful APIs and microservices'\n      }, {\n        name: 'JavaScript/TypeScript',\n        level: 92,\n        icon: SiTypescript,\n        color: '#3178C6',\n        description: 'Modern ES6+ JavaScript and TypeScript for type-safe development'\n      }, {\n        name: 'Python/Django',\n        level: 85,\n        icon: FaPython,\n        color: '#3776AB',\n        description: 'Full-stack Python development with Django and Flask'\n      }, {\n        name: 'MongoDB/PostgreSQL',\n        level: 88,\n        icon: SiMongodb,\n        color: '#47A248',\n        description: 'Database design and optimization for both SQL and NoSQL'\n      }, {\n        name: 'AWS/Cloud Services',\n        level: 82,\n        icon: FaAws,\n        color: '#FF9900',\n        description: 'Cloud infrastructure and serverless architecture'\n      }]\n    },\n    design: {\n      title: 'Design & Tools',\n      icon: FaFigma,\n      color: 'from-purple-500 to-pink-500',\n      skills: [{\n        name: 'UI/UX Design',\n        level: 85,\n        icon: FaFigma,\n        color: '#F24E1E',\n        description: 'User-centered design with modern UI/UX principles'\n      }, {\n        name: 'Figma/Adobe XD',\n        level: 80,\n        icon: FaFigma,\n        color: '#F24E1E',\n        description: 'Professional design tools for prototyping and collaboration'\n      }, {\n        name: 'Responsive Design',\n        level: 95,\n        icon: FaMobile,\n        color: '#38BDF8',\n        description: 'Mobile-first responsive design across all devices'\n      }, {\n        name: 'CSS/Tailwind',\n        level: 92,\n        icon: SiTailwindcss,\n        color: '#06B6D4',\n        description: 'Modern CSS with utility-first frameworks'\n      }, {\n        name: 'Git/GitHub',\n        level: 90,\n        icon: FaGitAlt,\n        color: '#F05032',\n        description: 'Version control and collaborative development workflows'\n      }, {\n        name: 'Docker/DevOps',\n        level: 75,\n        icon: FaDocker,\n        color: '#2496ED',\n        description: 'Containerization and CI/CD pipeline management'\n      }]\n    },\n    soft: {\n      title: 'Soft Skills',\n      icon: '🤝',\n      color: 'from-green-500 to-emerald-500',\n      skills: [{\n        name: 'Problem Solving',\n        level: 95,\n        icon: '🧩',\n        color: '#10B981',\n        description: 'Analytical thinking and creative solution development'\n      }, {\n        name: 'Team Collaboration',\n        level: 92,\n        icon: '👥',\n        color: '#3B82F6',\n        description: 'Effective teamwork and cross-functional collaboration'\n      }, {\n        name: 'Communication',\n        level: 88,\n        icon: '💬',\n        color: '#8B5CF6',\n        description: 'Clear technical communication with stakeholders'\n      }, {\n        name: 'Project Management',\n        level: 85,\n        icon: '📋',\n        color: '#F59E0B',\n        description: 'Agile methodologies and project delivery'\n      }, {\n        name: 'Leadership',\n        level: 82,\n        icon: '👑',\n        color: '#EF4444',\n        description: 'Technical leadership and mentoring junior developers'\n      }, {\n        name: 'Adaptability',\n        level: 90,\n        icon: '🔄',\n        color: '#06B6D4',\n        description: 'Quick adaptation to new technologies and methodologies'\n      }]\n    }\n  };\n  const tabs = Object.keys(skillCategories);\n\n  // Animation variants\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n  const itemVariants = {\n    hidden: {\n      opacity: 0,\n      y: 20\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"skills\",\n    className: \"py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full blur-3xl\",\n        animate: {\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 20,\n          repeat: Infinity,\n          ease: \"linear\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 82,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 81,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n      ref: ref,\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.span, {\n          className: \"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          whileInView: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            delay: 0.2\n          },\n          children: \"\\uD83D\\uDE80 My Expertise\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-white mb-6\",\n          children: [\"Skills &\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\",\n            children: \" Expertise\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"w-20 h-1 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto mb-6\",\n          initial: {\n            width: 0\n          },\n          whileInView: {\n            width: 80\n          },\n          transition: {\n            delay: 0.4,\n            duration: 0.8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-400 max-w-3xl mx-auto\",\n          children: \"A comprehensive showcase of my technical abilities, design skills, and professional expertise\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 124,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 97,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"flex justify-center mb-12\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-full p-2 shadow-lg\",\n          children: tabs.map(tab => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveTab(tab),\n            className: `px-6 py-3 rounded-full font-semibold transition-all duration-300 ${activeTab === tab ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md' : 'text-gray-600 hover:text-blue-600'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"mr-2\",\n              children: skillCategories[tab].icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 147,\n              columnNumber: 17\n            }, this), skillCategories[tab].title]\n          }, tab, true, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        animate: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.5\n        },\n        className: \"bg-white rounded-3xl p-8 shadow-xl\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n          children: skillCategories[activeTab].skills.map((skill, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              x: -20\n            },\n            animate: {\n              opacity: 1,\n              x: 0\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            className: \"group\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between mb-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl mr-3\",\n                  children: skill.icon\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold text-gray-800\",\n                  children: skill.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500 font-medium\",\n                children: [skill.level, \"%\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full bg-gray-200 rounded-full h-3 overflow-hidden\",\n              children: /*#__PURE__*/_jsxDEV(motion.div, {\n                initial: {\n                  width: 0\n                },\n                animate: {\n                  width: `${skill.level}%`\n                },\n                transition: {\n                  duration: 1,\n                  delay: index * 0.1\n                },\n                className: \"h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full relative\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"absolute inset-0 bg-white opacity-20 rounded-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 17\n            }, this)]\n          }, skill.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 164,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 162,\n          columnNumber: 11\n        }, this)\n      }, activeTab, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl text-white\",\n              children: \"\\uD83D\\uDE80\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 202,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-2\",\n            children: \"Fast Learner\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Always eager to learn new technologies and adapt to changing requirements\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl text-white\",\n              children: \"\\u26A1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-2\",\n            children: \"Performance Focused\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Committed to writing efficient, scalable code that performs well\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl text-white\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 223,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-2\",\n            children: \"Detail Oriented\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Meticulous attention to detail ensures high-quality deliverables\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.6\n        },\n        className: \"mt-16 text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-2xl font-bold text-gray-900 mb-8\",\n          children: \"Certifications & Learning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-wrap justify-center gap-4\",\n          children: ['AWS Certified Developer', 'React Professional', 'Node.js Certified', 'MongoDB University', 'Google Cloud Platform'].map((cert, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              duration: 0.3,\n              delay: index * 0.1\n            },\n            className: \"px-6 py-3 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow duration-300\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-gray-700 font-medium\",\n              children: cert\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 17\n            }, this)\n          }, cert, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(Skills, \"osddoNbMruFpkMZmyVLSxHlA4m4=\", false, function () {\n  return [useInView];\n});\n_c = Skills;\nexport default Skills;\nvar _c;\n$RefreshReg$(_c, \"Skills\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "motion", "useInView", "FaReact", "FaNodeJs", "FaJs", "FaPython", "FaDatabase", "FaAws", "FaFigma", "FaMobile", "FaGitAlt", "<PERSON>aDock<PERSON>", "SiTypescript", "SiTailwindcss", "SiMongodb", "SiPostgresql", "jsxDEV", "_jsxDEV", "Skills", "_s", "activeTab", "setActiveTab", "hoveredSkill", "setHoveredSkill", "ref", "isInView", "once", "threshold", "skillCategories", "technical", "title", "icon", "color", "skills", "name", "level", "description", "design", "soft", "tabs", "Object", "keys", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "duration", "id", "className", "children", "div", "animate", "scale", "rotate", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "whileInView", "span", "delay", "width", "map", "tab", "onClick", "skill", "index", "x", "cert", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/Skills.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport { motion, useInView } from 'framer-motion';\nimport { FaReact, FaNodeJs, FaJs, FaPython, FaDatabase, FaAws, FaFigma, FaMobile, FaGitAlt, FaDocker } from 'react-icons/fa';\nimport { SiTypescript, SiTailwindcss, SiMongodb, SiPostgresql } from 'react-icons/si';\n\nconst Skills = () => {\n  const [activeTab, setActiveTab] = useState('technical');\n  const [hoveredSkill, setHoveredSkill] = useState(null);\n  const ref = useRef(null);\n  const isInView = useInView(ref, { once: true, threshold: 0.1 });\n\n  const skillCategories = {\n    technical: {\n      title: 'Technical Skills',\n      icon: FaReact,\n      color: 'from-blue-500 to-cyan-500',\n      skills: [\n        { name: 'React/Next.js', level: 95, icon: FaReact, color: '#61DAFB', description: 'Advanced React development with hooks, context, and performance optimization' },\n        { name: 'Node.js/Express', level: 90, icon: FaNodeJs, color: '#339933', description: 'Backend development with RESTful APIs and microservices' },\n        { name: 'JavaScript/TypeScript', level: 92, icon: SiTypescript, color: '#3178C6', description: 'Modern ES6+ JavaScript and TypeScript for type-safe development' },\n        { name: 'Python/Django', level: 85, icon: FaPython, color: '#3776AB', description: 'Full-stack Python development with Django and Flask' },\n        { name: 'MongoDB/PostgreSQL', level: 88, icon: SiMongodb, color: '#47A248', description: 'Database design and optimization for both SQL and NoSQL' },\n        { name: 'AWS/Cloud Services', level: 82, icon: FaAws, color: '#FF9900', description: 'Cloud infrastructure and serverless architecture' }\n      ]\n    },\n    design: {\n      title: 'Design & Tools',\n      icon: FaFigma,\n      color: 'from-purple-500 to-pink-500',\n      skills: [\n        { name: 'UI/UX Design', level: 85, icon: FaFigma, color: '#F24E1E', description: 'User-centered design with modern UI/UX principles' },\n        { name: 'Figma/Adobe XD', level: 80, icon: FaFigma, color: '#F24E1E', description: 'Professional design tools for prototyping and collaboration' },\n        { name: 'Responsive Design', level: 95, icon: FaMobile, color: '#38BDF8', description: 'Mobile-first responsive design across all devices' },\n        { name: 'CSS/Tailwind', level: 92, icon: SiTailwindcss, color: '#06B6D4', description: 'Modern CSS with utility-first frameworks' },\n        { name: 'Git/GitHub', level: 90, icon: FaGitAlt, color: '#F05032', description: 'Version control and collaborative development workflows' },\n        { name: 'Docker/DevOps', level: 75, icon: FaDocker, color: '#2496ED', description: 'Containerization and CI/CD pipeline management' }\n      ]\n    },\n    soft: {\n      title: 'Soft Skills',\n      icon: '🤝',\n      color: 'from-green-500 to-emerald-500',\n      skills: [\n        { name: 'Problem Solving', level: 95, icon: '🧩', color: '#10B981', description: 'Analytical thinking and creative solution development' },\n        { name: 'Team Collaboration', level: 92, icon: '👥', color: '#3B82F6', description: 'Effective teamwork and cross-functional collaboration' },\n        { name: 'Communication', level: 88, icon: '💬', color: '#8B5CF6', description: 'Clear technical communication with stakeholders' },\n        { name: 'Project Management', level: 85, icon: '📋', color: '#F59E0B', description: 'Agile methodologies and project delivery' },\n        { name: 'Leadership', level: 82, icon: '👑', color: '#EF4444', description: 'Technical leadership and mentoring junior developers' },\n        { name: 'Adaptability', level: 90, icon: '🔄', color: '#06B6D4', description: 'Quick adaptation to new technologies and methodologies' }\n      ]\n    }\n  };\n\n  const tabs = Object.keys(skillCategories);\n\n  // Animation variants\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.5\n      }\n    }\n  };\n\n  return (\n    <section id=\"skills\" className=\"py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden\">\n      {/* Background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-600/10 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 20,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\" ref={ref}>\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            className=\"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-400 text-sm font-medium mb-4\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            🚀 My Expertise\n          </motion.span>\n\n          <h2 className=\"text-4xl md:text-5xl font-bold text-white mb-6\">\n            Skills &\n            <span className=\"bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent\"> Expertise</span>\n          </h2>\n\n          <motion.div\n            className=\"w-20 h-1 bg-gradient-to-r from-blue-400 to-purple-400 mx-auto mb-6\"\n            initial={{ width: 0 }}\n            whileInView={{ width: 80 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n          />\n\n          <p className=\"text-xl text-gray-400 max-w-3xl mx-auto\">\n            A comprehensive showcase of my technical abilities, design skills, and professional expertise\n          </p>\n        </motion.div>\n\n        {/* Tab Navigation */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex justify-center mb-12\"\n        >\n          <div className=\"bg-white rounded-full p-2 shadow-lg\">\n            {tabs.map((tab) => (\n              <button\n                key={tab}\n                onClick={() => setActiveTab(tab)}\n                className={`px-6 py-3 rounded-full font-semibold transition-all duration-300 ${\n                  activeTab === tab\n                    ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-md'\n                    : 'text-gray-600 hover:text-blue-600'\n                }`}\n              >\n                <span className=\"mr-2\">{skillCategories[tab].icon}</span>\n                {skillCategories[tab].title}\n              </button>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Skills Content */}\n        <motion.div\n          key={activeTab}\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n          className=\"bg-white rounded-3xl p-8 shadow-xl\"\n        >\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            {skillCategories[activeTab].skills.map((skill, index) => (\n              <motion.div\n                key={skill.name}\n                initial={{ opacity: 0, x: -20 }}\n                animate={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                className=\"group\"\n              >\n                <div className=\"flex items-center justify-between mb-3\">\n                  <div className=\"flex items-center\">\n                    <span className=\"text-2xl mr-3\">{skill.icon}</span>\n                    <span className=\"font-semibold text-gray-800\">{skill.name}</span>\n                  </div>\n                  <span className=\"text-sm text-gray-500 font-medium\">{skill.level}%</span>\n                </div>\n                \n                <div className=\"w-full bg-gray-200 rounded-full h-3 overflow-hidden\">\n                  <motion.div\n                    initial={{ width: 0 }}\n                    animate={{ width: `${skill.level}%` }}\n                    transition={{ duration: 1, delay: index * 0.1 }}\n                    className=\"h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full relative\"\n                  >\n                    <div className=\"absolute inset-0 bg-white opacity-20 rounded-full\"></div>\n                  </motion.div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Additional Info */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\"\n        >\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl text-white\">🚀</span>\n            </div>\n            <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Fast Learner</h3>\n            <p className=\"text-gray-600\">\n              Always eager to learn new technologies and adapt to changing requirements\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl text-white\">⚡</span>\n            </div>\n            <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Performance Focused</h3>\n            <p className=\"text-gray-600\">\n              Committed to writing efficient, scalable code that performs well\n            </p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-4\">\n              <span className=\"text-2xl text-white\">🎯</span>\n            </div>\n            <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Detail Oriented</h3>\n            <p className=\"text-gray-600\">\n              Meticulous attention to detail ensures high-quality deliverables\n            </p>\n          </div>\n        </motion.div>\n\n        {/* Certifications */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.6 }}\n          className=\"mt-16 text-center\"\n        >\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-8\">Certifications & Learning</h3>\n          <div className=\"flex flex-wrap justify-center gap-4\">\n            {[\n              'AWS Certified Developer',\n              'React Professional',\n              'Node.js Certified',\n              'MongoDB University',\n              'Google Cloud Platform'\n            ].map((cert, index) => (\n              <motion.div\n                key={cert}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ duration: 0.3, delay: index * 0.1 }}\n                className=\"px-6 py-3 bg-white rounded-full shadow-md hover:shadow-lg transition-shadow duration-300\"\n              >\n                <span className=\"text-gray-700 font-medium\">{cert}</span>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Skills;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SAASC,MAAM,EAAEC,SAAS,QAAQ,eAAe;AACjD,SAASC,OAAO,EAAEC,QAAQ,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,gBAAgB;AAC5H,SAASC,YAAY,EAAEC,aAAa,EAAEC,SAAS,EAAEC,YAAY,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtF,MAAMC,MAAM,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnB,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGxB,QAAQ,CAAC,WAAW,CAAC;EACvD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAM2B,GAAG,GAAG1B,MAAM,CAAC,IAAI,CAAC;EACxB,MAAM2B,QAAQ,GAAGxB,SAAS,CAACuB,GAAG,EAAE;IAAEE,IAAI,EAAE,IAAI;IAAEC,SAAS,EAAE;EAAI,CAAC,CAAC;EAE/D,MAAMC,eAAe,GAAG;IACtBC,SAAS,EAAE;MACTC,KAAK,EAAE,kBAAkB;MACzBC,IAAI,EAAE7B,OAAO;MACb8B,KAAK,EAAE,2BAA2B;MAClCC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAE7B,OAAO;QAAE8B,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAA+E,CAAC,EAClK;QAAEF,IAAI,EAAE,iBAAiB;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAE5B,QAAQ;QAAE6B,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAA0D,CAAC,EAChJ;QAAEF,IAAI,EAAE,uBAAuB;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAEnB,YAAY;QAAEoB,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAkE,CAAC,EAClK;QAAEF,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAE1B,QAAQ;QAAE2B,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAsD,CAAC,EAC1I;QAAEF,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAEjB,SAAS;QAAEkB,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAA0D,CAAC,EACpJ;QAAEF,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAExB,KAAK;QAAEyB,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAmD,CAAC;IAE7I,CAAC;IACDC,MAAM,EAAE;MACNP,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAEvB,OAAO;MACbwB,KAAK,EAAE,6BAA6B;MACpCC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAEvB,OAAO;QAAEwB,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAoD,CAAC,EACtI;QAAEF,IAAI,EAAE,gBAAgB;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAEvB,OAAO;QAAEwB,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAA8D,CAAC,EAClJ;QAAEF,IAAI,EAAE,mBAAmB;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAEtB,QAAQ;QAAEuB,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAoD,CAAC,EAC5I;QAAEF,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAElB,aAAa;QAAEmB,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAA2C,CAAC,EACnI;QAAEF,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAErB,QAAQ;QAAEsB,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAA0D,CAAC,EAC3I;QAAEF,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAEpB,QAAQ;QAAEqB,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAiD,CAAC;IAEzI,CAAC;IACDE,IAAI,EAAE;MACJR,KAAK,EAAE,aAAa;MACpBC,IAAI,EAAE,IAAI;MACVC,KAAK,EAAE,+BAA+B;MACtCC,MAAM,EAAE,CACN;QAAEC,IAAI,EAAE,iBAAiB;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAwD,CAAC,EAC1I;QAAEF,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAwD,CAAC,EAC7I;QAAEF,IAAI,EAAE,eAAe;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAkD,CAAC,EAClI;QAAEF,IAAI,EAAE,oBAAoB;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAA2C,CAAC,EAChI;QAAEF,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAuD,CAAC,EACpI;QAAEF,IAAI,EAAE,cAAc;QAAEC,KAAK,EAAE,EAAE;QAAEJ,IAAI,EAAE,IAAI;QAAEC,KAAK,EAAE,SAAS;QAAEI,WAAW,EAAE;MAAyD,CAAC;IAE5I;EACF,CAAC;EAED,MAAMG,IAAI,GAAGC,MAAM,CAACC,IAAI,CAACb,eAAe,CAAC;;EAEzC;EACA,MAAMc,iBAAiB,GAAG;IACxBC,MAAM,EAAE;MAAEC,OAAO,EAAE;IAAE,CAAC;IACtBC,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVE,UAAU,EAAE;QACVC,eAAe,EAAE;MACnB;IACF;EACF,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBL,MAAM,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEK,CAAC,EAAE;IAAG,CAAC;IAC7BJ,OAAO,EAAE;MACPD,OAAO,EAAE,CAAC;MACVK,CAAC,EAAE,CAAC;MACJH,UAAU,EAAE;QACVI,QAAQ,EAAE;MACZ;IACF;EACF,CAAC;EAED,oBACEjC,OAAA;IAASkC,EAAE,EAAC,QAAQ;IAACC,SAAS,EAAC,6FAA6F;IAAAC,QAAA,gBAE1HpC,OAAA;MAAKmC,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/CpC,OAAA,CAACjB,MAAM,CAACsD,GAAG;QACTF,SAAS,EAAC,+GAA+G;QACzHG,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFX,UAAU,EAAE;UACVI,QAAQ,EAAE,EAAE;UACZQ,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN/C,OAAA;MAAKmC,SAAS,EAAC,sDAAsD;MAAC5B,GAAG,EAAEA,GAAI;MAAA6B,QAAA,gBAC7EpC,OAAA,CAACjB,MAAM,CAACsD,GAAG;QACTW,OAAO,EAAE;UAAErB,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAG,CAAE;QAC/BiB,WAAW,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAE,CAAE;QAClCH,UAAU,EAAE;UAAEI,QAAQ,EAAE;QAAI,CAAE;QAC9BE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BpC,OAAA,CAACjB,MAAM,CAACmE,IAAI;UACVf,SAAS,EAAC,yJAAyJ;UACnKa,OAAO,EAAE;YAAErB,OAAO,EAAE,CAAC;YAAEY,KAAK,EAAE;UAAI,CAAE;UACpCU,WAAW,EAAE;YAAEtB,OAAO,EAAE,CAAC;YAAEY,KAAK,EAAE;UAAE,CAAE;UACtCV,UAAU,EAAE;YAAEsB,KAAK,EAAE;UAAI,CAAE;UAAAf,QAAA,EAC5B;QAED;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAEd/C,OAAA;UAAImC,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAAC,UAE7D,eAAApC,OAAA;YAAMmC,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5G,CAAC,eAEL/C,OAAA,CAACjB,MAAM,CAACsD,GAAG;UACTF,SAAS,EAAC,oEAAoE;UAC9Ea,OAAO,EAAE;YAAEI,KAAK,EAAE;UAAE,CAAE;UACtBH,WAAW,EAAE;YAAEG,KAAK,EAAE;UAAG,CAAE;UAC3BvB,UAAU,EAAE;YAAEsB,KAAK,EAAE,GAAG;YAAElB,QAAQ,EAAE;UAAI;QAAE;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAEF/C,OAAA;UAAGmC,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb/C,OAAA,CAACjB,MAAM,CAACsD,GAAG;QACTW,OAAO,EAAE;UAAErB,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAG,CAAE;QAC/BiB,WAAW,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAE,CAAE;QAClCH,UAAU,EAAE;UAAEI,QAAQ,EAAE,GAAG;UAAEkB,KAAK,EAAE;QAAI,CAAE;QAC1ChB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eAErCpC,OAAA;UAAKmC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjDd,IAAI,CAAC+B,GAAG,CAAEC,GAAG,iBACZtD,OAAA;YAEEuD,OAAO,EAAEA,CAAA,KAAMnD,YAAY,CAACkD,GAAG,CAAE;YACjCnB,SAAS,EAAE,oEACThC,SAAS,KAAKmD,GAAG,GACb,mEAAmE,GACnE,mCAAmC,EACtC;YAAAlB,QAAA,gBAEHpC,OAAA;cAAMmC,SAAS,EAAC,MAAM;cAAAC,QAAA,EAAEzB,eAAe,CAAC2C,GAAG,CAAC,CAACxC;YAAI;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACxDpC,eAAe,CAAC2C,GAAG,CAAC,CAACzC,KAAK;UAAA,GATtByC,GAAG;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUF,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb/C,OAAA,CAACjB,MAAM,CAACsD,GAAG;QAETW,OAAO,EAAE;UAAErB,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAG,CAAE;QAC/BM,OAAO,EAAE;UAAEX,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAE,CAAE;QAC9BH,UAAU,EAAE;UAAEI,QAAQ,EAAE;QAAI,CAAE;QAC9BE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,eAE9CpC,OAAA;UAAKmC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDzB,eAAe,CAACR,SAAS,CAAC,CAACa,MAAM,CAACqC,GAAG,CAAC,CAACG,KAAK,EAAEC,KAAK,kBAClDzD,OAAA,CAACjB,MAAM,CAACsD,GAAG;YAETW,OAAO,EAAE;cAAErB,OAAO,EAAE,CAAC;cAAE+B,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCpB,OAAO,EAAE;cAAEX,OAAO,EAAE,CAAC;cAAE+B,CAAC,EAAE;YAAE,CAAE;YAC9B7B,UAAU,EAAE;cAAEI,QAAQ,EAAE,GAAG;cAAEkB,KAAK,EAAEM,KAAK,GAAG;YAAI,CAAE;YAClDtB,SAAS,EAAC,OAAO;YAAAC,QAAA,gBAEjBpC,OAAA;cAAKmC,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDpC,OAAA;gBAAKmC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCpC,OAAA;kBAAMmC,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAEoB,KAAK,CAAC1C;gBAAI;kBAAA8B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACnD/C,OAAA;kBAAMmC,SAAS,EAAC,6BAA6B;kBAAAC,QAAA,EAAEoB,KAAK,CAACvC;gBAAI;kBAAA2B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D,CAAC,eACN/C,OAAA;gBAAMmC,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,GAAEoB,KAAK,CAACtC,KAAK,EAAC,GAAC;cAAA;gBAAA0B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAEN/C,OAAA;cAAKmC,SAAS,EAAC,qDAAqD;cAAAC,QAAA,eAClEpC,OAAA,CAACjB,MAAM,CAACsD,GAAG;gBACTW,OAAO,EAAE;kBAAEI,KAAK,EAAE;gBAAE,CAAE;gBACtBd,OAAO,EAAE;kBAAEc,KAAK,EAAE,GAAGI,KAAK,CAACtC,KAAK;gBAAI,CAAE;gBACtCW,UAAU,EAAE;kBAAEI,QAAQ,EAAE,CAAC;kBAAEkB,KAAK,EAAEM,KAAK,GAAG;gBAAI,CAAE;gBAChDtB,SAAS,EAAC,2EAA2E;gBAAAC,QAAA,eAErFpC,OAAA;kBAAKmC,SAAS,EAAC;gBAAmD;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA,GAvBDS,KAAK,CAACvC,IAAI;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAwBL,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC,GAnCD5C,SAAS;QAAAyC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAoCJ,CAAC,eAGb/C,OAAA,CAACjB,MAAM,CAACsD,GAAG;QACTW,OAAO,EAAE;UAAErB,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAG,CAAE;QAC/BiB,WAAW,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAE,CAAE;QAClCH,UAAU,EAAE;UAAEI,QAAQ,EAAE,GAAG;UAAEkB,KAAK,EAAE;QAAI,CAAE;QAC1ChB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAEvDpC,OAAA;UAAKmC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BpC,OAAA;YAAKmC,SAAS,EAAC,mHAAmH;YAAAC,QAAA,eAChIpC,OAAA;cAAMmC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACN/C,OAAA;YAAImC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAY;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtE/C,OAAA;YAAGmC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN/C,OAAA;UAAKmC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BpC,OAAA;YAAKmC,SAAS,EAAC,kHAAkH;YAAAC,QAAA,eAC/HpC,OAAA;cAAMmC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eACN/C,OAAA;YAAImC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAmB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7E/C,OAAA;YAAGmC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC,eAEN/C,OAAA;UAAKmC,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BpC,OAAA;YAAKmC,SAAS,EAAC,mHAAmH;YAAAC,QAAA,eAChIpC,OAAA;cAAMmC,SAAS,EAAC,qBAAqB;cAAAC,QAAA,EAAC;YAAE;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACN/C,OAAA;YAAImC,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAAe;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzE/C,OAAA;YAAGmC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAE7B;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb/C,OAAA,CAACjB,MAAM,CAACsD,GAAG;QACTW,OAAO,EAAE;UAAErB,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAG,CAAE;QAC/BiB,WAAW,EAAE;UAAEtB,OAAO,EAAE,CAAC;UAAEK,CAAC,EAAE;QAAE,CAAE;QAClCH,UAAU,EAAE;UAAEI,QAAQ,EAAE,GAAG;UAAEkB,KAAK,EAAE;QAAI,CAAE;QAC1ChB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BpC,OAAA;UAAImC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EAAC;QAAyB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACpF/C,OAAA;UAAKmC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EACjD,CACC,yBAAyB,EACzB,oBAAoB,EACpB,mBAAmB,EACnB,oBAAoB,EACpB,uBAAuB,CACxB,CAACiB,GAAG,CAAC,CAACM,IAAI,EAAEF,KAAK,kBAChBzD,OAAA,CAACjB,MAAM,CAACsD,GAAG;YAETW,OAAO,EAAE;cAAErB,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAI,CAAE;YACpCU,WAAW,EAAE;cAAEtB,OAAO,EAAE,CAAC;cAAEY,KAAK,EAAE;YAAE,CAAE;YACtCV,UAAU,EAAE;cAAEI,QAAQ,EAAE,GAAG;cAAEkB,KAAK,EAAEM,KAAK,GAAG;YAAI,CAAE;YAClDtB,SAAS,EAAC,0FAA0F;YAAAC,QAAA,eAEpGpC,OAAA;cAAMmC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEuB;YAAI;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC,GANpDY,IAAI;YAAAf,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOC,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC7C,EAAA,CAjQID,MAAM;EAAA,QAIOjB,SAAS;AAAA;AAAA4E,EAAA,GAJtB3D,MAAM;AAmQZ,eAAeA,MAAM;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}