{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Portfolio my\\\\src\\\\components\\\\Projects.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGithub, FaExternalLinkAlt, FaCode, FaEye, FaStar, FaCalendarAlt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Projects = () => {\n  _s();\n  const [filter, setFilter] = useState('all');\n  const [hoveredProject, setHoveredProject] = useState(null);\n  const projects = [{\n    id: 1,\n    title: 'E-Commerce Platform',\n    description: 'A comprehensive full-stack e-commerce solution featuring modern UI/UX, secure payment processing, real-time inventory management, and advanced analytics dashboard.',\n    longDescription: 'Built with React and Node.js, this platform handles thousands of products and users. Features include JWT authentication, Stripe payments, email notifications, and a powerful admin panel with sales analytics.',\n    image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',\n    technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'JWT', 'Socket.io'],\n    category: 'fullstack',\n    github: '#',\n    demo: '#',\n    featured: true,\n    status: 'Live',\n    date: '2023',\n    stats: {\n      stars: 124,\n      forks: 45,\n      views: '2.1k'\n    }\n  }, {\n    id: 2,\n    title: 'AI-Powered Task Manager',\n    description: 'An intelligent task management application with AI-driven priority suggestions, real-time collaboration, and advanced analytics.',\n    longDescription: 'Features include drag-and-drop Kanban boards, AI task prioritization, team chat, file sharing, and detailed productivity analytics. Built with modern React patterns and Firebase real-time database.',\n    image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop',\n    technologies: ['React', 'Firebase', 'OpenAI API', 'Material-UI', 'Chart.js'],\n    category: 'frontend',\n    github: '#',\n    demo: '#',\n    featured: true,\n    status: 'In Development',\n    date: '2023',\n    stats: {\n      stars: 89,\n      forks: 23,\n      views: '1.5k'\n    }\n  }, {\n    id: 3,\n    title: 'Real-Time Weather Analytics',\n    description: 'A comprehensive weather dashboard with predictive analytics, interactive maps, and personalized weather insights.',\n    longDescription: 'Integrates multiple weather APIs to provide accurate forecasts, severe weather alerts, and climate trend analysis. Features include location-based services, data visualization, and mobile-responsive design.',\n    image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=800&h=600&fit=crop',\n    technologies: ['React', 'OpenWeather API', 'D3.js', 'Mapbox', 'PWA'],\n    category: 'frontend',\n    github: '#',\n    demo: '#',\n    featured: false,\n    status: 'Live',\n    date: '2023',\n    stats: {\n      stars: 67,\n      forks: 18,\n      views: '980'\n    }\n  }, {\n    id: 4,\n    title: 'Microservices API Gateway',\n    description: 'A scalable REST API gateway with microservices architecture, advanced security, and comprehensive monitoring.',\n    longDescription: 'Enterprise-grade API server with JWT authentication, rate limiting, request/response logging, API documentation, and health monitoring. Supports multiple databases and caching strategies.',\n    image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?w=800&h=600&fit=crop',\n    technologies: ['Node.js', 'Express', 'Redis', 'Docker', 'Swagger', 'JWT'],\n    category: 'backend',\n    github: '#',\n    demo: '#',\n    featured: true,\n    status: 'Live',\n    date: '2022',\n    stats: {\n      stars: 156,\n      forks: 67,\n      views: '3.2k'\n    }\n  }, {\n    id: 5,\n    title: 'Social Media Analytics Hub',\n    description: 'An advanced social media analytics platform with AI-powered insights, automated reporting, and multi-platform integration.',\n    longDescription: 'Comprehensive dashboard that aggregates data from multiple social platforms, provides sentiment analysis, engagement metrics, and automated report generation. Features real-time data processing and predictive analytics.',\n    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',\n    technologies: ['React', 'D3.js', 'Node.js', 'PostgreSQL', 'Python', 'TensorFlow'],\n    category: 'fullstack',\n    github: '#',\n    demo: '#',\n    featured: false,\n    status: 'Live',\n    date: '2023',\n    stats: {\n      stars: 92,\n      forks: 34,\n      views: '1.8k'\n    }\n  }, {\n    id: 6,\n    title: 'Cloud-Native Mobile Backend',\n    description: 'Highly scalable serverless backend infrastructure with real-time messaging, push notifications, and global CDN.',\n    longDescription: 'Built on AWS Lambda and DynamoDB, this backend supports millions of users with real-time chat, file uploads, push notifications, and advanced caching. Includes comprehensive monitoring and auto-scaling.',\n    image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',\n    technologies: ['Node.js', 'AWS Lambda', 'DynamoDB', 'Socket.io', 'Redis', 'CloudFront'],\n    category: 'backend',\n    github: '#',\n    demo: '#',\n    featured: false,\n    status: 'Live',\n    date: '2022',\n    stats: {\n      stars: 78,\n      forks: 29,\n      views: '1.2k'\n    }\n  }];\n  const categories = [{\n    id: 'all',\n    name: 'All Projects',\n    count: projects.length\n  }, {\n    id: 'fullstack',\n    name: 'Full Stack',\n    count: projects.filter(p => p.category === 'fullstack').length\n  }, {\n    id: 'frontend',\n    name: 'Frontend',\n    count: projects.filter(p => p.category === 'frontend').length\n  }, {\n    id: 'backend',\n    name: 'Backend',\n    count: projects.filter(p => p.category === 'backend').length\n  }];\n  const filteredProjects = filter === 'all' ? projects : projects.filter(project => project.category === filter);\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"projects\",\n    className: \"py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute inset-0 overflow-hidden\",\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        className: \"absolute top-1/3 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/5 to-purple-600/5 rounded-full blur-3xl\",\n        animate: {\n          scale: [1, 1.2, 1],\n          rotate: [0, 180, 360]\n        },\n        transition: {\n          duration: 25,\n          repeat: Infinity,\n          ease: \"linear\"\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(motion.span, {\n          className: \"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-600 text-sm font-medium mb-4\",\n          initial: {\n            opacity: 0,\n            scale: 0.8\n          },\n          whileInView: {\n            opacity: 1,\n            scale: 1\n          },\n          transition: {\n            delay: 0.2\n          },\n          children: \"\\uD83D\\uDCBC My Portfolio\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n          children: [\"Featured\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\",\n            children: \" Projects\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 149,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\",\n          initial: {\n            width: 0\n          },\n          whileInView: {\n            width: 80\n          },\n          transition: {\n            delay: 0.4,\n            duration: 0.8\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n          children: \"A curated selection of my best work showcasing technical expertise, creative problem-solving, and modern development practices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 132,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.2\n        },\n        className: \"flex flex-wrap justify-center gap-4 mb-12\",\n        children: categories.map((category, index) => /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: () => setFilter(category.id),\n          className: `relative px-6 py-3 rounded-full font-semibold transition-all duration-300 ${filter === category.id ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg' : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:shadow-lg border border-gray-200'}`,\n          whileHover: {\n            scale: 1.05,\n            y: -2\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            delay: index * 0.1\n          },\n          children: [category.name, /*#__PURE__*/_jsxDEV(\"span\", {\n            className: `ml-2 px-2 py-1 rounded-full text-xs ${filter === category.id ? 'bg-white/20 text-white' : 'bg-blue-100 text-blue-600'}`,\n            children: category.count\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), filter === category.id && /*#__PURE__*/_jsxDEV(motion.div, {\n            className: \"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full\",\n            layoutId: \"activeFilter\",\n            transition: {\n              type: \"spring\",\n              bounce: 0.2,\n              duration: 0.6\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 17\n          }, this)]\n        }, category.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 172,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n          initial: {\n            opacity: 0\n          },\n          animate: {\n            opacity: 1\n          },\n          exit: {\n            opacity: 0\n          },\n          transition: {\n            duration: 0.3\n          },\n          children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 30,\n              scale: 0.9\n            },\n            animate: {\n              opacity: 1,\n              y: 0,\n              scale: 1\n            },\n            exit: {\n              opacity: 0,\n              y: -30,\n              scale: 0.9\n            },\n            transition: {\n              duration: 0.5,\n              delay: index * 0.1\n            },\n            whileHover: {\n              y: -10,\n              scale: 1.02\n            },\n            className: \"group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100\",\n            onMouseEnter: () => setHoveredProject(project.id),\n            onMouseLeave: () => setHoveredProject(null),\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute top-4 left-4 z-20 flex gap-2\",\n              children: [project.featured && /*#__PURE__*/_jsxDEV(motion.span, {\n                className: \"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg\",\n                initial: {\n                  scale: 0\n                },\n                animate: {\n                  scale: 1\n                },\n                transition: {\n                  delay: 0.3\n                },\n                children: \"\\u2B50 Featured\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: `px-3 py-1 rounded-full text-sm font-medium shadow-lg ${project.status === 'Live' ? 'bg-green-500 text-white' : 'bg-blue-500 text-white'}`,\n                children: project.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative h-56 overflow-hidden\",\n              children: [/*#__PURE__*/_jsxDEV(motion.img, {\n                src: project.image,\n                alt: project.title,\n                className: \"w-full h-full object-cover\",\n                whileHover: {\n                  scale: 1.1\n                },\n                transition: {\n                  duration: 0.6\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute top-4 right-4 flex gap-2\",\n                initial: {\n                  opacity: 0,\n                  x: 20\n                },\n                animate: {\n                  opacity: hoveredProject === project.id ? 1 : 0,\n                  x: hoveredProject === project.id ? 0 : 20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs flex items-center gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(FaStar, {\n                    className: \"text-yellow-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 23\n                  }, this), project.stats.stars]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs flex items-center gap-1\",\n                  children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 274,\n                    columnNumber: 23\n                  }, this), project.stats.views]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 273,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                className: \"absolute inset-0 flex items-center justify-center gap-4\",\n                initial: {\n                  opacity: 0,\n                  y: 20\n                },\n                animate: {\n                  opacity: hoveredProject === project.id ? 1 : 0,\n                  y: hoveredProject === project.id ? 0 : 20\n                },\n                transition: {\n                  duration: 0.3\n                },\n                children: [/*#__PURE__*/_jsxDEV(motion.a, {\n                  href: project.github,\n                  className: \"p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors shadow-lg\",\n                  whileHover: {\n                    scale: 1.1\n                  },\n                  whileTap: {\n                    scale: 0.9\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FaGithub, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 295,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n                  href: project.demo,\n                  className: \"p-3 bg-blue-600 rounded-full text-white hover:bg-blue-700 transition-colors shadow-lg\",\n                  whileHover: {\n                    scale: 1.1\n                  },\n                  whileTap: {\n                    scale: 0.9\n                  },\n                  children: /*#__PURE__*/_jsxDEV(FaExternalLinkAlt, {\n                    className: \"w-5 h-5\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 303,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start justify-between mb-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors flex-1\",\n                  children: project.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 311,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-1 text-sm text-gray-500\",\n                  children: [/*#__PURE__*/_jsxDEV(FaCalendarAlt, {\n                    className: \"w-3 h-3\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 23\n                  }, this), project.date]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 310,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 mb-4 text-sm leading-relaxed\",\n                children: hoveredProject === project.id && project.longDescription ? project.longDescription : project.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-wrap gap-2 mb-4\",\n                children: project.technologies.map((tech, techIndex) => /*#__PURE__*/_jsxDEV(motion.span, {\n                  className: \"px-3 py-1 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 rounded-full text-xs font-medium border border-blue-100 hover:border-blue-300 transition-colors\",\n                  initial: {\n                    opacity: 0,\n                    scale: 0.8\n                  },\n                  animate: {\n                    opacity: 1,\n                    scale: 1\n                  },\n                  transition: {\n                    delay: techIndex * 0.05\n                  },\n                  whileHover: {\n                    scale: 1.05\n                  },\n                  children: tech\n                }, tech, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 329,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-100\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(FaStar, {\n                      className: \"text-yellow-500\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 346,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: project.stats.stars\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 347,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 345,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(FaCode, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 350,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: project.stats.forks\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 351,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 349,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(FaEye, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 354,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      children: project.stats.views\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 355,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 353,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 344,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-xs text-gray-400\",\n                  children: project.category\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 358,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 343,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 309,\n              columnNumber: 17\n            }, this)]\n          }, project.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 217,\n            columnNumber: 15\n          }, this))\n        }, filter, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"text-center mt-20\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-12 border border-blue-100\",\n          children: /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.9\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: 0.2\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-3xl font-bold text-gray-900 mb-4\",\n              children: \"Ready to work together?\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\",\n              children: \"I'm always excited to take on new challenges and collaborate on innovative projects. Let's discuss how we can bring your ideas to life!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex flex-col sm:flex-row gap-4 justify-center items-center\",\n              children: [/*#__PURE__*/_jsxDEV(motion.a, {\n                href: \"https://github.com\",\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                className: \"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-xl transition-all duration-300 group\",\n                whileHover: {\n                  scale: 1.05,\n                  y: -2\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: [/*#__PURE__*/_jsxDEV(FaGithub, {\n                  className: \"w-5 h-5 mr-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 398,\n                  columnNumber: 19\n                }, this), \"View All Projects\", /*#__PURE__*/_jsxDEV(motion.div, {\n                  className: \"ml-2\",\n                  animate: {\n                    x: [0, 5, 0]\n                  },\n                  transition: {\n                    duration: 1.5,\n                    repeat: Infinity\n                  },\n                  children: \"\\u2192\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(motion.a, {\n                href: \"#contact\",\n                className: \"inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-full font-semibold hover:border-blue-600 hover:text-blue-600 transition-all duration-300\",\n                whileHover: {\n                  scale: 1.05,\n                  y: -2\n                },\n                whileTap: {\n                  scale: 0.95\n                },\n                children: [\"Get In Touch\", /*#__PURE__*/_jsxDEV(FaExternalLinkAlt, {\n                  className: \"w-4 h-4 ml-2\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 416,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 369,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 114,\n    columnNumber: 5\n  }, this);\n};\n_s(Projects, \"+nsqUx+vE0MNXlvo+vNSlxJg/rY=\");\n_c = Projects;\nexport default Projects;\nvar _c;\n$RefreshReg$(_c, \"Projects\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FaExternalLinkAlt", "FaCode", "FaEye", "FaStar", "FaCalendarAlt", "jsxDEV", "_jsxDEV", "Projects", "_s", "filter", "setFilter", "hoveredProject", "setHoveredProject", "projects", "id", "title", "description", "longDescription", "image", "technologies", "category", "github", "demo", "featured", "status", "date", "stats", "stars", "forks", "views", "categories", "name", "count", "length", "p", "filteredProjects", "project", "className", "children", "div", "animate", "scale", "rotate", "transition", "duration", "repeat", "Infinity", "ease", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "initial", "opacity", "y", "whileInView", "span", "delay", "width", "map", "index", "button", "onClick", "whileHover", "whileTap", "layoutId", "type", "bounce", "mode", "exit", "onMouseEnter", "onMouseLeave", "img", "src", "alt", "x", "a", "href", "tech", "techIndex", "target", "rel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Portfolio my/src/components/Projects.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { FaGithub, FaExternalLinkAlt, FaCode, FaEye, FaStar, FaCalendarAlt } from 'react-icons/fa';\n\nconst Projects = () => {\n  const [filter, setFilter] = useState('all');\n  const [hoveredProject, setHoveredProject] = useState(null);\n\n  const projects = [\n    {\n      id: 1,\n      title: 'E-Commerce Platform',\n      description: 'A comprehensive full-stack e-commerce solution featuring modern UI/UX, secure payment processing, real-time inventory management, and advanced analytics dashboard.',\n      longDescription: 'Built with React and Node.js, this platform handles thousands of products and users. Features include JWT authentication, Stripe payments, email notifications, and a powerful admin panel with sales analytics.',\n      image: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop',\n      technologies: ['React', 'Node.js', 'MongoDB', 'Stripe', 'JWT', 'Socket.io'],\n      category: 'fullstack',\n      github: '#',\n      demo: '#',\n      featured: true,\n      status: 'Live',\n      date: '2023',\n      stats: { stars: 124, forks: 45, views: '2.1k' }\n    },\n    {\n      id: 2,\n      title: 'AI-Powered Task Manager',\n      description: 'An intelligent task management application with AI-driven priority suggestions, real-time collaboration, and advanced analytics.',\n      longDescription: 'Features include drag-and-drop Kanban boards, AI task prioritization, team chat, file sharing, and detailed productivity analytics. Built with modern React patterns and Firebase real-time database.',\n      image: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=800&h=600&fit=crop',\n      technologies: ['React', 'Firebase', 'OpenAI API', 'Material-UI', 'Chart.js'],\n      category: 'frontend',\n      github: '#',\n      demo: '#',\n      featured: true,\n      status: 'In Development',\n      date: '2023',\n      stats: { stars: 89, forks: 23, views: '1.5k' }\n    },\n    {\n      id: 3,\n      title: 'Real-Time Weather Analytics',\n      description: 'A comprehensive weather dashboard with predictive analytics, interactive maps, and personalized weather insights.',\n      longDescription: 'Integrates multiple weather APIs to provide accurate forecasts, severe weather alerts, and climate trend analysis. Features include location-based services, data visualization, and mobile-responsive design.',\n      image: 'https://images.unsplash.com/photo-1504608524841-42fe6f032b4b?w=800&h=600&fit=crop',\n      technologies: ['React', 'OpenWeather API', 'D3.js', 'Mapbox', 'PWA'],\n      category: 'frontend',\n      github: '#',\n      demo: '#',\n      featured: false,\n      status: 'Live',\n      date: '2023',\n      stats: { stars: 67, forks: 18, views: '980' }\n    },\n    {\n      id: 4,\n      title: 'Microservices API Gateway',\n      description: 'A scalable REST API gateway with microservices architecture, advanced security, and comprehensive monitoring.',\n      longDescription: 'Enterprise-grade API server with JWT authentication, rate limiting, request/response logging, API documentation, and health monitoring. Supports multiple databases and caching strategies.',\n      image: 'https://images.unsplash.com/photo-**********-ef010cbdcc31?w=800&h=600&fit=crop',\n      technologies: ['Node.js', 'Express', 'Redis', 'Docker', 'Swagger', 'JWT'],\n      category: 'backend',\n      github: '#',\n      demo: '#',\n      featured: true,\n      status: 'Live',\n      date: '2022',\n      stats: { stars: 156, forks: 67, views: '3.2k' }\n    },\n    {\n      id: 5,\n      title: 'Social Media Analytics Hub',\n      description: 'An advanced social media analytics platform with AI-powered insights, automated reporting, and multi-platform integration.',\n      longDescription: 'Comprehensive dashboard that aggregates data from multiple social platforms, provides sentiment analysis, engagement metrics, and automated report generation. Features real-time data processing and predictive analytics.',\n      image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=600&fit=crop',\n      technologies: ['React', 'D3.js', 'Node.js', 'PostgreSQL', 'Python', 'TensorFlow'],\n      category: 'fullstack',\n      github: '#',\n      demo: '#',\n      featured: false,\n      status: 'Live',\n      date: '2023',\n      stats: { stars: 92, forks: 34, views: '1.8k' }\n    },\n    {\n      id: 6,\n      title: 'Cloud-Native Mobile Backend',\n      description: 'Highly scalable serverless backend infrastructure with real-time messaging, push notifications, and global CDN.',\n      longDescription: 'Built on AWS Lambda and DynamoDB, this backend supports millions of users with real-time chat, file uploads, push notifications, and advanced caching. Includes comprehensive monitoring and auto-scaling.',\n      image: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',\n      technologies: ['Node.js', 'AWS Lambda', 'DynamoDB', 'Socket.io', 'Redis', 'CloudFront'],\n      category: 'backend',\n      github: '#',\n      demo: '#',\n      featured: false,\n      status: 'Live',\n      date: '2022',\n      stats: { stars: 78, forks: 29, views: '1.2k' }\n    }\n  ];\n\n  const categories = [\n    { id: 'all', name: 'All Projects', count: projects.length },\n    { id: 'fullstack', name: 'Full Stack', count: projects.filter(p => p.category === 'fullstack').length },\n    { id: 'frontend', name: 'Frontend', count: projects.filter(p => p.category === 'frontend').length },\n    { id: 'backend', name: 'Backend', count: projects.filter(p => p.category === 'backend').length }\n  ];\n\n  const filteredProjects = filter === 'all' \n    ? projects \n    : projects.filter(project => project.category === filter);\n\n  return (\n    <section id=\"projects\" className=\"py-20 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden\">\n      {/* Background elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute top-1/3 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/5 to-purple-600/5 rounded-full blur-3xl\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 25,\n            repeat: Infinity,\n            ease: \"linear\"\n          }}\n        />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"text-center mb-16\"\n        >\n          <motion.span\n            className=\"inline-block px-4 py-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-full text-blue-600 text-sm font-medium mb-4\"\n            initial={{ opacity: 0, scale: 0.8 }}\n            whileInView={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2 }}\n          >\n            💼 My Portfolio\n          </motion.span>\n\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Featured\n            <span className=\"bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent\"> Projects</span>\n          </h2>\n\n          <motion.div\n            className=\"w-20 h-1 bg-gradient-to-r from-blue-600 to-purple-600 mx-auto mb-6\"\n            initial={{ width: 0 }}\n            whileInView={{ width: 80 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n          />\n\n          <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n            A curated selection of my best work showcasing technical expertise, creative problem-solving, and modern development practices\n          </p>\n        </motion.div>\n\n        {/* Filter Buttons */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.2 }}\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\n        >\n          {categories.map((category, index) => (\n            <motion.button\n              key={category.id}\n              onClick={() => setFilter(category.id)}\n              className={`relative px-6 py-3 rounded-full font-semibold transition-all duration-300 ${\n                filter === category.id\n                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'\n                  : 'bg-white text-gray-700 hover:bg-gray-50 shadow-md hover:shadow-lg border border-gray-200'\n              }`}\n              whileHover={{ scale: 1.05, y: -2 }}\n              whileTap={{ scale: 0.95 }}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              {category.name}\n              <span className={`ml-2 px-2 py-1 rounded-full text-xs ${\n                filter === category.id\n                  ? 'bg-white/20 text-white'\n                  : 'bg-blue-100 text-blue-600'\n              }`}>\n                {category.count}\n              </span>\n\n              {filter === category.id && (\n                <motion.div\n                  className=\"absolute inset-0 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-full\"\n                  layoutId=\"activeFilter\"\n                  transition={{ type: \"spring\", bounce: 0.2, duration: 0.6 }}\n                />\n              )}\n            </motion.button>\n          ))}\n        </motion.div>\n\n        {/* Projects Grid */}\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={filter}\n            className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            {filteredProjects.map((project, index) => (\n              <motion.div\n                key={project.id}\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\n                animate={{ opacity: 1, y: 0, scale: 1 }}\n                exit={{ opacity: 0, y: -30, scale: 0.9 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n                whileHover={{ y: -10, scale: 1.02 }}\n                className=\"group relative bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 overflow-hidden border border-gray-100\"\n                onMouseEnter={() => setHoveredProject(project.id)}\n                onMouseLeave={() => setHoveredProject(null)}\n              >\n                {/* Status and Featured badges */}\n                <div className=\"absolute top-4 left-4 z-20 flex gap-2\">\n                  {project.featured && (\n                    <motion.span\n                      className=\"bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg\"\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ delay: 0.3 }}\n                    >\n                      ⭐ Featured\n                    </motion.span>\n                  )}\n                  <span className={`px-3 py-1 rounded-full text-sm font-medium shadow-lg ${\n                    project.status === 'Live'\n                      ? 'bg-green-500 text-white'\n                      : 'bg-blue-500 text-white'\n                  }`}>\n                    {project.status}\n                  </span>\n                </div>\n\n                {/* Project Image */}\n                <div className=\"relative h-56 overflow-hidden\">\n                  <motion.img\n                    src={project.image}\n                    alt={project.title}\n                    className=\"w-full h-full object-cover\"\n                    whileHover={{ scale: 1.1 }}\n                    transition={{ duration: 0.6 }}\n                  />\n\n                  {/* Gradient overlay */}\n                  <div className=\"absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n\n                  {/* Stats overlay */}\n                  <motion.div\n                    className=\"absolute top-4 right-4 flex gap-2\"\n                    initial={{ opacity: 0, x: 20 }}\n                    animate={{ opacity: hoveredProject === project.id ? 1 : 0, x: hoveredProject === project.id ? 0 : 20 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <div className=\"bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs flex items-center gap-1\">\n                      <FaStar className=\"text-yellow-400\" />\n                      {project.stats.stars}\n                    </div>\n                    <div className=\"bg-black/50 backdrop-blur-sm text-white px-2 py-1 rounded-lg text-xs flex items-center gap-1\">\n                      <FaEye />\n                      {project.stats.views}\n                    </div>\n                  </motion.div>\n\n                  {/* Action buttons */}\n                  <motion.div\n                    className=\"absolute inset-0 flex items-center justify-center gap-4\"\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{\n                      opacity: hoveredProject === project.id ? 1 : 0,\n                      y: hoveredProject === project.id ? 0 : 20\n                    }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <motion.a\n                      href={project.github}\n                      className=\"p-3 bg-white/90 backdrop-blur-sm rounded-full text-gray-800 hover:bg-white transition-colors shadow-lg\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      <FaGithub className=\"w-5 h-5\" />\n                    </motion.a>\n                    <motion.a\n                      href={project.demo}\n                      className=\"p-3 bg-blue-600 rounded-full text-white hover:bg-blue-700 transition-colors shadow-lg\"\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      <FaExternalLinkAlt className=\"w-5 h-5\" />\n                    </motion.a>\n                  </motion.div>\n                </div>\n\n                {/* Project Content */}\n                <div className=\"p-6\">\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <h3 className=\"text-xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors flex-1\">\n                      {project.title}\n                    </h3>\n                    <div className=\"flex items-center gap-1 text-sm text-gray-500\">\n                      <FaCalendarAlt className=\"w-3 h-3\" />\n                      {project.date}\n                    </div>\n                  </div>\n\n                  <p className=\"text-gray-600 mb-4 text-sm leading-relaxed\">\n                    {hoveredProject === project.id && project.longDescription\n                      ? project.longDescription\n                      : project.description}\n                  </p>\n\n                  {/* Technologies */}\n                  <div className=\"flex flex-wrap gap-2 mb-4\">\n                    {project.technologies.map((tech, techIndex) => (\n                      <motion.span\n                        key={tech}\n                        className=\"px-3 py-1 bg-gradient-to-r from-blue-50 to-purple-50 text-blue-700 rounded-full text-xs font-medium border border-blue-100 hover:border-blue-300 transition-colors\"\n                        initial={{ opacity: 0, scale: 0.8 }}\n                        animate={{ opacity: 1, scale: 1 }}\n                        transition={{ delay: techIndex * 0.05 }}\n                        whileHover={{ scale: 1.05 }}\n                      >\n                        {tech}\n                      </motion.span>\n                    ))}\n                  </div>\n\n                  {/* Project stats */}\n                  <div className=\"flex items-center justify-between text-sm text-gray-500 pt-4 border-t border-gray-100\">\n                    <div className=\"flex items-center gap-4\">\n                      <div className=\"flex items-center gap-1\">\n                        <FaStar className=\"text-yellow-500\" />\n                        <span>{project.stats.stars}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <FaCode />\n                        <span>{project.stats.forks}</span>\n                      </div>\n                      <div className=\"flex items-center gap-1\">\n                        <FaEye />\n                        <span>{project.stats.views}</span>\n                      </div>\n                    </div>\n                    <div className=\"text-xs text-gray-400\">\n                      {project.category}\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </AnimatePresence>\n\n        {/* Call to Action */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"text-center mt-20\"\n        >\n          <div className=\"bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-12 border border-blue-100\">\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9 }}\n              whileInView={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2 }}\n            >\n              <h3 className=\"text-3xl font-bold text-gray-900 mb-4\">\n                Ready to work together?\n              </h3>\n              <p className=\"text-lg text-gray-600 mb-8 max-w-2xl mx-auto\">\n                I'm always excited to take on new challenges and collaborate on innovative projects.\n                Let's discuss how we can bring your ideas to life!\n              </p>\n\n              <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n                <motion.a\n                  href=\"https://github.com\"\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"inline-flex items-center px-8 py-4 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full font-semibold hover:shadow-xl transition-all duration-300 group\"\n                  whileHover={{ scale: 1.05, y: -2 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <FaGithub className=\"w-5 h-5 mr-2\" />\n                  View All Projects\n                  <motion.div\n                    className=\"ml-2\"\n                    animate={{ x: [0, 5, 0] }}\n                    transition={{ duration: 1.5, repeat: Infinity }}\n                  >\n                    →\n                  </motion.div>\n                </motion.a>\n\n                <motion.a\n                  href=\"#contact\"\n                  className=\"inline-flex items-center px-8 py-4 border-2 border-gray-300 text-gray-700 rounded-full font-semibold hover:border-blue-600 hover:text-blue-600 transition-all duration-300\"\n                  whileHover={{ scale: 1.05, y: -2 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  Get In Touch\n                  <FaExternalLinkAlt className=\"w-4 h-4 ml-2\" />\n                </motion.a>\n              </div>\n            </motion.div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default Projects;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,QAAQ,EAAEC,iBAAiB,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,aAAa,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnG,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACe,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAE1D,MAAMiB,QAAQ,GAAG,CACf;IACEC,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,qKAAqK;IAClLC,eAAe,EAAE,kNAAkN;IACnOC,KAAK,EAAE,gFAAgF;IACvFC,YAAY,EAAE,CAAC,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,CAAC;IAC3EC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAChD,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,yBAAyB;IAChCC,WAAW,EAAE,kIAAkI;IAC/IC,eAAe,EAAE,uMAAuM;IACxNC,KAAK,EAAE,mFAAmF;IAC1FC,YAAY,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,CAAC;IAC5EC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,gBAAgB;IACxBC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAC/C,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,mHAAmH;IAChIC,eAAe,EAAE,gNAAgN;IACjOC,KAAK,EAAE,mFAAmF;IAC1FC,YAAY,EAAE,CAAC,OAAO,EAAE,iBAAiB,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,CAAC;IACpEC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAM;EAC9C,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,+GAA+G;IAC5HC,eAAe,EAAE,6LAA6L;IAC9MC,KAAK,EAAE,gFAAgF;IACvFC,YAAY,EAAE,CAAC,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,KAAK,CAAC;IACzEC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAChD,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,4BAA4B;IACnCC,WAAW,EAAE,4HAA4H;IACzIC,eAAe,EAAE,6NAA6N;IAC9OC,KAAK,EAAE,mFAAmF;IAC1FC,YAAY,EAAE,CAAC,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,EAAE,QAAQ,EAAE,YAAY,CAAC;IACjFC,QAAQ,EAAE,WAAW;IACrBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAC/C,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,iHAAiH;IAC9HC,eAAe,EAAE,4MAA4M;IAC7NC,KAAK,EAAE,gFAAgF;IACvFC,YAAY,EAAE,CAAC,SAAS,EAAE,YAAY,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,YAAY,CAAC;IACvFC,QAAQ,EAAE,SAAS;IACnBC,MAAM,EAAE,GAAG;IACXC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,KAAK;IACfC,MAAM,EAAE,MAAM;IACdC,IAAI,EAAE,MAAM;IACZC,KAAK,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAO;EAC/C,CAAC,CACF;EAED,MAAMC,UAAU,GAAG,CACjB;IAAEhB,EAAE,EAAE,KAAK;IAAEiB,IAAI,EAAE,cAAc;IAAEC,KAAK,EAAEnB,QAAQ,CAACoB;EAAO,CAAC,EAC3D;IAAEnB,EAAE,EAAE,WAAW;IAAEiB,IAAI,EAAE,YAAY;IAAEC,KAAK,EAAEnB,QAAQ,CAACJ,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAACd,QAAQ,KAAK,WAAW,CAAC,CAACa;EAAO,CAAC,EACvG;IAAEnB,EAAE,EAAE,UAAU;IAAEiB,IAAI,EAAE,UAAU;IAAEC,KAAK,EAAEnB,QAAQ,CAACJ,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAACd,QAAQ,KAAK,UAAU,CAAC,CAACa;EAAO,CAAC,EACnG;IAAEnB,EAAE,EAAE,SAAS;IAAEiB,IAAI,EAAE,SAAS;IAAEC,KAAK,EAAEnB,QAAQ,CAACJ,MAAM,CAACyB,CAAC,IAAIA,CAAC,CAACd,QAAQ,KAAK,SAAS,CAAC,CAACa;EAAO,CAAC,CACjG;EAED,MAAME,gBAAgB,GAAG1B,MAAM,KAAK,KAAK,GACrCI,QAAQ,GACRA,QAAQ,CAACJ,MAAM,CAAC2B,OAAO,IAAIA,OAAO,CAAChB,QAAQ,KAAKX,MAAM,CAAC;EAE3D,oBACEH,OAAA;IAASQ,EAAE,EAAC,UAAU;IAACuB,SAAS,EAAC,oFAAoF;IAAAC,QAAA,gBAEnHhC,OAAA;MAAK+B,SAAS,EAAC,kCAAkC;MAAAC,QAAA,eAC/ChC,OAAA,CAACT,MAAM,CAAC0C,GAAG;QACTF,SAAS,EAAC,4GAA4G;QACtHG,OAAO,EAAE;UACPC,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,GAAG;QACtB,CAAE;QACFC,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,MAAM,EAAEC,QAAQ;UAChBC,IAAI,EAAE;QACR;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN7C,OAAA;MAAK+B,SAAS,EAAC,sDAAsD;MAAAC,QAAA,gBACnEhC,OAAA,CAACT,MAAM,CAAC0C,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BP,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BhC,OAAA,CAACT,MAAM,CAAC2D,IAAI;UACVnB,SAAS,EAAC,yJAAyJ;UACnKe,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE;UAAI,CAAE;UACpCc,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEZ,KAAK,EAAE;UAAE,CAAE;UACtCE,UAAU,EAAE;YAAEc,KAAK,EAAE;UAAI,CAAE;UAAAnB,QAAA,EAC5B;QAED;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAa,CAAC,eAEd7C,OAAA;UAAI+B,SAAS,EAAC,mDAAmD;UAAAC,QAAA,GAAC,UAEhE,eAAAhC,OAAA;YAAM+B,SAAS,EAAC,4EAA4E;YAAAC,QAAA,EAAC;UAAS;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3G,CAAC,eAEL7C,OAAA,CAACT,MAAM,CAAC0C,GAAG;UACTF,SAAS,EAAC,oEAAoE;UAC9Ee,OAAO,EAAE;YAAEM,KAAK,EAAE;UAAE,CAAE;UACtBH,WAAW,EAAE;YAAEG,KAAK,EAAE;UAAG,CAAE;UAC3Bf,UAAU,EAAE;YAAEc,KAAK,EAAE,GAAG;YAAEb,QAAQ,EAAE;UAAI;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAEF7C,OAAA;UAAG+B,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EAAC;QAEvD;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb7C,OAAA,CAACT,MAAM,CAAC0C,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEa,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAEpDR,UAAU,CAAC6B,GAAG,CAAC,CAACvC,QAAQ,EAAEwC,KAAK,kBAC9BtD,OAAA,CAACT,MAAM,CAACgE,MAAM;UAEZC,OAAO,EAAEA,CAAA,KAAMpD,SAAS,CAACU,QAAQ,CAACN,EAAE,CAAE;UACtCuB,SAAS,EAAE,6EACT5B,MAAM,KAAKW,QAAQ,CAACN,EAAE,GAClB,mEAAmE,GACnE,0FAA0F,EAC7F;UACHiD,UAAU,EAAE;YAAEtB,KAAK,EAAE,IAAI;YAAEa,CAAC,EAAE,CAAC;UAAE,CAAE;UACnCU,QAAQ,EAAE;YAAEvB,KAAK,EAAE;UAAK,CAAE;UAC1BW,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/Bd,OAAO,EAAE;YAAEa,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BX,UAAU,EAAE;YAAEc,KAAK,EAAEG,KAAK,GAAG;UAAI,CAAE;UAAAtB,QAAA,GAElClB,QAAQ,CAACW,IAAI,eACdzB,OAAA;YAAM+B,SAAS,EAAE,uCACf5B,MAAM,KAAKW,QAAQ,CAACN,EAAE,GAClB,wBAAwB,GACxB,2BAA2B,EAC9B;YAAAwB,QAAA,EACAlB,QAAQ,CAACY;UAAK;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CAAC,EAEN1C,MAAM,KAAKW,QAAQ,CAACN,EAAE,iBACrBR,OAAA,CAACT,MAAM,CAAC0C,GAAG;YACTF,SAAS,EAAC,kFAAkF;YAC5F4B,QAAQ,EAAC,cAAc;YACvBtB,UAAU,EAAE;cAAEuB,IAAI,EAAE,QAAQ;cAAEC,MAAM,EAAE,GAAG;cAAEvB,QAAQ,EAAE;YAAI;UAAE;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CACF;QAAA,GA5BI/B,QAAQ,CAACN,EAAE;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BH,CAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGb7C,OAAA,CAACR,eAAe;QAACsE,IAAI,EAAC,MAAM;QAAA9B,QAAA,eAC1BhC,OAAA,CAACT,MAAM,CAAC0C,GAAG;UAETF,SAAS,EAAC,sDAAsD;UAChEe,OAAO,EAAE;YAAEC,OAAO,EAAE;UAAE,CAAE;UACxBb,OAAO,EAAE;YAAEa,OAAO,EAAE;UAAE,CAAE;UACxBgB,IAAI,EAAE;YAAEhB,OAAO,EAAE;UAAE,CAAE;UACrBV,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAN,QAAA,EAE7BH,gBAAgB,CAACwB,GAAG,CAAC,CAACvB,OAAO,EAAEwB,KAAK,kBACnCtD,OAAA,CAACT,MAAM,CAAC0C,GAAG;YAETa,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,EAAE;cAAEb,KAAK,EAAE;YAAI,CAAE;YAC3CD,OAAO,EAAE;cAAEa,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC;cAAEb,KAAK,EAAE;YAAE,CAAE;YACxC4B,IAAI,EAAE;cAAEhB,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE,CAAC,EAAE;cAAEb,KAAK,EAAE;YAAI,CAAE;YACzCE,UAAU,EAAE;cAAEC,QAAQ,EAAE,GAAG;cAAEa,KAAK,EAAEG,KAAK,GAAG;YAAI,CAAE;YAClDG,UAAU,EAAE;cAAET,CAAC,EAAE,CAAC,EAAE;cAAEb,KAAK,EAAE;YAAK,CAAE;YACpCJ,SAAS,EAAC,mIAAmI;YAC7IiC,YAAY,EAAEA,CAAA,KAAM1D,iBAAiB,CAACwB,OAAO,CAACtB,EAAE,CAAE;YAClDyD,YAAY,EAAEA,CAAA,KAAM3D,iBAAiB,CAAC,IAAI,CAAE;YAAA0B,QAAA,gBAG5ChC,OAAA;cAAK+B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,GACnDF,OAAO,CAACb,QAAQ,iBACfjB,OAAA,CAACT,MAAM,CAAC2D,IAAI;gBACVnB,SAAS,EAAC,8GAA8G;gBACxHe,OAAO,EAAE;kBAAEX,KAAK,EAAE;gBAAE,CAAE;gBACtBD,OAAO,EAAE;kBAAEC,KAAK,EAAE;gBAAE,CAAE;gBACtBE,UAAU,EAAE;kBAAEc,KAAK,EAAE;gBAAI,CAAE;gBAAAnB,QAAA,EAC5B;cAED;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAa,CACd,eACD7C,OAAA;gBAAM+B,SAAS,EAAE,wDACfD,OAAO,CAACZ,MAAM,KAAK,MAAM,GACrB,yBAAyB,GACzB,wBAAwB,EAC3B;gBAAAc,QAAA,EACAF,OAAO,CAACZ;cAAM;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGN7C,OAAA;cAAK+B,SAAS,EAAC,+BAA+B;cAAAC,QAAA,gBAC5ChC,OAAA,CAACT,MAAM,CAAC2E,GAAG;gBACTC,GAAG,EAAErC,OAAO,CAAClB,KAAM;gBACnBwD,GAAG,EAAEtC,OAAO,CAACrB,KAAM;gBACnBsB,SAAS,EAAC,4BAA4B;gBACtC0B,UAAU,EAAE;kBAAEtB,KAAK,EAAE;gBAAI,CAAE;gBAC3BE,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/B,CAAC,eAGF7C,OAAA;gBAAK+B,SAAS,EAAC;cAA+I;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAGjK7C,OAAA,CAACT,MAAM,CAAC0C,GAAG;gBACTF,SAAS,EAAC,mCAAmC;gBAC7Ce,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEsB,CAAC,EAAE;gBAAG,CAAE;gBAC/BnC,OAAO,EAAE;kBAAEa,OAAO,EAAE1C,cAAc,KAAKyB,OAAO,CAACtB,EAAE,GAAG,CAAC,GAAG,CAAC;kBAAE6D,CAAC,EAAEhE,cAAc,KAAKyB,OAAO,CAACtB,EAAE,GAAG,CAAC,GAAG;gBAAG,CAAE;gBACvG6B,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAN,QAAA,gBAE9BhC,OAAA;kBAAK+B,SAAS,EAAC,8FAA8F;kBAAAC,QAAA,gBAC3GhC,OAAA,CAACH,MAAM;oBAACkC,SAAS,EAAC;kBAAiB;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACrCf,OAAO,CAACV,KAAK,CAACC,KAAK;gBAAA;kBAAAqB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC,eACN7C,OAAA;kBAAK+B,SAAS,EAAC,8FAA8F;kBAAAC,QAAA,gBAC3GhC,OAAA,CAACJ,KAAK;oBAAA8C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACRf,OAAO,CAACV,KAAK,CAACG,KAAK;gBAAA;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eAGb7C,OAAA,CAACT,MAAM,CAAC0C,GAAG;gBACTF,SAAS,EAAC,yDAAyD;gBACnEe,OAAO,EAAE;kBAAEC,OAAO,EAAE,CAAC;kBAAEC,CAAC,EAAE;gBAAG,CAAE;gBAC/Bd,OAAO,EAAE;kBACPa,OAAO,EAAE1C,cAAc,KAAKyB,OAAO,CAACtB,EAAE,GAAG,CAAC,GAAG,CAAC;kBAC9CwC,CAAC,EAAE3C,cAAc,KAAKyB,OAAO,CAACtB,EAAE,GAAG,CAAC,GAAG;gBACzC,CAAE;gBACF6B,UAAU,EAAE;kBAAEC,QAAQ,EAAE;gBAAI,CAAE;gBAAAN,QAAA,gBAE9BhC,OAAA,CAACT,MAAM,CAAC+E,CAAC;kBACPC,IAAI,EAAEzC,OAAO,CAACf,MAAO;kBACrBgB,SAAS,EAAC,wGAAwG;kBAClH0B,UAAU,EAAE;oBAAEtB,KAAK,EAAE;kBAAI,CAAE;kBAC3BuB,QAAQ,EAAE;oBAAEvB,KAAK,EAAE;kBAAI,CAAE;kBAAAH,QAAA,eAEzBhC,OAAA,CAACP,QAAQ;oBAACsC,SAAS,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxB,CAAC,eACX7C,OAAA,CAACT,MAAM,CAAC+E,CAAC;kBACPC,IAAI,EAAEzC,OAAO,CAACd,IAAK;kBACnBe,SAAS,EAAC,uFAAuF;kBACjG0B,UAAU,EAAE;oBAAEtB,KAAK,EAAE;kBAAI,CAAE;kBAC3BuB,QAAQ,EAAE;oBAAEvB,KAAK,EAAE;kBAAI,CAAE;kBAAAH,QAAA,eAEzBhC,OAAA,CAACN,iBAAiB;oBAACqC,SAAS,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAGN7C,OAAA;cAAK+B,SAAS,EAAC,KAAK;cAAAC,QAAA,gBAClBhC,OAAA;gBAAK+B,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpDhC,OAAA;kBAAI+B,SAAS,EAAC,oFAAoF;kBAAAC,QAAA,EAC/FF,OAAO,CAACrB;gBAAK;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACL7C,OAAA;kBAAK+B,SAAS,EAAC,+CAA+C;kBAAAC,QAAA,gBAC5DhC,OAAA,CAACF,aAAa;oBAACiC,SAAS,EAAC;kBAAS;oBAAAW,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACpCf,OAAO,CAACX,IAAI;gBAAA;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACV,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAEN7C,OAAA;gBAAG+B,SAAS,EAAC,4CAA4C;gBAAAC,QAAA,EACtD3B,cAAc,KAAKyB,OAAO,CAACtB,EAAE,IAAIsB,OAAO,CAACnB,eAAe,GACrDmB,OAAO,CAACnB,eAAe,GACvBmB,OAAO,CAACpB;cAAW;gBAAAgC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtB,CAAC,eAGJ7C,OAAA;gBAAK+B,SAAS,EAAC,2BAA2B;gBAAAC,QAAA,EACvCF,OAAO,CAACjB,YAAY,CAACwC,GAAG,CAAC,CAACmB,IAAI,EAAEC,SAAS,kBACxCzE,OAAA,CAACT,MAAM,CAAC2D,IAAI;kBAEVnB,SAAS,EAAC,oKAAoK;kBAC9Ke,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEZ,KAAK,EAAE;kBAAI,CAAE;kBACpCD,OAAO,EAAE;oBAAEa,OAAO,EAAE,CAAC;oBAAEZ,KAAK,EAAE;kBAAE,CAAE;kBAClCE,UAAU,EAAE;oBAAEc,KAAK,EAAEsB,SAAS,GAAG;kBAAK,CAAE;kBACxChB,UAAU,EAAE;oBAAEtB,KAAK,EAAE;kBAAK,CAAE;kBAAAH,QAAA,EAE3BwC;gBAAI,GAPAA,IAAI;kBAAA9B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQE,CACd;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,eAGN7C,OAAA;gBAAK+B,SAAS,EAAC,uFAAuF;gBAAAC,QAAA,gBACpGhC,OAAA;kBAAK+B,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,gBACtChC,OAAA;oBAAK+B,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtChC,OAAA,CAACH,MAAM;sBAACkC,SAAS,EAAC;oBAAiB;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACtC7C,OAAA;sBAAAgC,QAAA,EAAOF,OAAO,CAACV,KAAK,CAACC;oBAAK;sBAAAqB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACN7C,OAAA;oBAAK+B,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtChC,OAAA,CAACL,MAAM;sBAAA+C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACV7C,OAAA;sBAAAgC,QAAA,EAAOF,OAAO,CAACV,KAAK,CAACE;oBAAK;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC,eACN7C,OAAA;oBAAK+B,SAAS,EAAC,yBAAyB;oBAAAC,QAAA,gBACtChC,OAAA,CAACJ,KAAK;sBAAA8C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACT7C,OAAA;sBAAAgC,QAAA,EAAOF,OAAO,CAACV,KAAK,CAACG;oBAAK;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/B,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7C,OAAA;kBAAK+B,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EACnCF,OAAO,CAAChB;gBAAQ;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA,GAhJDf,OAAO,CAACtB,EAAE;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAiJL,CACb;QAAC,GA3JG1C,MAAM;UAAAuC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA4JD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGlB7C,OAAA,CAACT,MAAM,CAAC0C,GAAG;QACTa,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCX,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEa,KAAK,EAAE;QAAI,CAAE;QAC1CpB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAE7BhC,OAAA;UAAK+B,SAAS,EAAC,oFAAoF;UAAAC,QAAA,eACjGhC,OAAA,CAACT,MAAM,CAAC0C,GAAG;YACTa,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAI,CAAE;YACpCc,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEZ,KAAK,EAAE;YAAE,CAAE;YACtCE,UAAU,EAAE;cAAEc,KAAK,EAAE;YAAI,CAAE;YAAAnB,QAAA,gBAE3BhC,OAAA;cAAI+B,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAEtD;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7C,OAAA;cAAG+B,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAG5D;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ7C,OAAA;cAAK+B,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAC1EhC,OAAA,CAACT,MAAM,CAAC+E,CAAC;gBACPC,IAAI,EAAC,oBAAoB;gBACzBG,MAAM,EAAC,QAAQ;gBACfC,GAAG,EAAC,qBAAqB;gBACzB5C,SAAS,EAAC,yKAAyK;gBACnL0B,UAAU,EAAE;kBAAEtB,KAAK,EAAE,IAAI;kBAAEa,CAAC,EAAE,CAAC;gBAAE,CAAE;gBACnCU,QAAQ,EAAE;kBAAEvB,KAAK,EAAE;gBAAK,CAAE;gBAAAH,QAAA,gBAE1BhC,OAAA,CAACP,QAAQ;kBAACsC,SAAS,EAAC;gBAAc;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,qBAErC,eAAA7C,OAAA,CAACT,MAAM,CAAC0C,GAAG;kBACTF,SAAS,EAAC,MAAM;kBAChBG,OAAO,EAAE;oBAAEmC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;kBAAE,CAAE;kBAC1BhC,UAAU,EAAE;oBAAEC,QAAQ,EAAE,GAAG;oBAAEC,MAAM,EAAEC;kBAAS,CAAE;kBAAAR,QAAA,EACjD;gBAED;kBAAAU,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL,CAAC,eAEX7C,OAAA,CAACT,MAAM,CAAC+E,CAAC;gBACPC,IAAI,EAAC,UAAU;gBACfxC,SAAS,EAAC,4KAA4K;gBACtL0B,UAAU,EAAE;kBAAEtB,KAAK,EAAE,IAAI;kBAAEa,CAAC,EAAE,CAAC;gBAAE,CAAE;gBACnCU,QAAQ,EAAE;kBAAEvB,KAAK,EAAE;gBAAK,CAAE;gBAAAH,QAAA,GAC3B,cAEC,eAAAhC,OAAA,CAACN,iBAAiB;kBAACqC,SAAS,EAAC;gBAAc;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC3C,EAAA,CApaID,QAAQ;AAAA2E,EAAA,GAAR3E,QAAQ;AAsad,eAAeA,QAAQ;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}